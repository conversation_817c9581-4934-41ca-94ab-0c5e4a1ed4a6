package com.sprt.android.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sprt.android.log.domain.HeartbeatLog;
import com.sprt.android.log.domain.PushLog;
import com.sprt.android.log.domain.PushLogApk;
import com.sprt.android.log.dto.PackageInfo;
import com.sprt.android.log.dto.PushLogDTO;
import com.sprt.android.log.mapper.HeartbeatLogMapper;
import com.sprt.android.log.mapper.PushLogApkMapper;
import com.sprt.android.log.mapper.PushLogMapper;
import com.sprt.android.log.service.PushLogService;
import com.sprt.android.log.vo.PushLogVO;
import com.sprt.common.utils.SecurityUtils;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.Device;
import com.sprt.pc.domain.GroupApk;
import com.sprt.pc.mapper.ApkMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupApkMapper;
import com.sprt.pc.vo.DeviceVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 推送日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class PushLogServiceImpl extends ServiceImpl<PushLogMapper, PushLog> implements PushLogService {
    @Autowired
    private PushLogMapper pushLogMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private GroupApkMapper groupApkMapper;

    @Autowired
    private HeartbeatLogMapper heartbeatLogMapper;

    @Autowired
    private ApkMapper apkMapper;

    @Autowired
    private PushLogApkMapper pushLogApkMapper;

    /**
     * 查询推送日志
     *
     * @param id 推送日志主键
     * @return 推送日志
     */
    @Override
    public PushLog selectPushLogById(Long id) {
        return pushLogMapper.selectPushLogById(id);
    }

    /**
     * 查询推送日志列表
     *
     * @param pushLog 推送日志
     * @return 推送日志
     */
    @Override
    public List<PushLogVO> selectPushLogList(PushLog pushLog) {
        List<PushLogVO> pushLogList = pushLogMapper.selectPushLogList(pushLog);

        // 查询已安装apk
        for (PushLogVO tempPushLog : pushLogList) {
            String[] deviceIdArray = tempPushLog.getDeviceIds().split(",");
            List<Long> apkIdList = new ArrayList<>();
            List<Device> deviceList = new ArrayList<>();
            for (String apkId : tempPushLog.getApkIds().split(",")) {
                apkIdList.add(Long.valueOf(apkId));
            }
            deviceList = deviceMapper.selectList(new LambdaQueryWrapper<Device>().in(Device::getId, Arrays.asList(deviceIdArray)));

            PushLog updatePushLog = pushLogMapper.selectPushLogById(tempPushLog.getId());
            int installedApkCount = 0;

            List<String> deviceSnList = deviceList.stream()
                    .map(Device::getSn)  // 获取每个设备的 sn
                    .filter(Objects::nonNull)  // 过滤掉 sn 为 null 的情况（可选）
                    .collect(Collectors.toList());// 收集为 List
            Map<String, HeartbeatLog> heartbeatLogMap;
            if(!deviceSnList.isEmpty()){
                List<HeartbeatLog> heartbeatLogList = heartbeatLogMapper.selectLastHeartbeatLogsBySnList(deviceSnList);
                heartbeatLogMap = heartbeatLogList.stream()
                        .collect(Collectors.toMap(HeartbeatLog::getSn, h -> h));
            }else {
                heartbeatLogMap = new HashMap<>();
            }


            for (Device device : deviceList) {
                HeartbeatLog heartbeatLog = heartbeatLogMap.get(device.getSn());
                String installedApkIds = "";
                if (heartbeatLog == null) {
                    continue;
                }
                String allPackage = heartbeatLog.getAllPackage();
                if (StringUtils.isEmpty(allPackage)) {
                    continue;
                }
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    PackageInfo[] packageInfoArray = mapper.readValue(allPackage, PackageInfo[].class);
                    for (PackageInfo packageInfo : packageInfoArray) {
                        Apk apk = apkMapper.selectOne(new LambdaQueryWrapper<Apk>()
                                .eq(Apk::getApkPackage, packageInfo.getPackageName())
                                .eq(Apk::getVersion, packageInfo.getVersionName()));
                        if (apk == null) {
                            continue;
                        }
                        if (StringUtils.isEmpty(installedApkIds)) {
                            installedApkIds += apk.getId();
                        } else {
                            installedApkIds += "," + apk.getId();
                        }
                        if (apkIdList.contains(apk.getId())) {
                            installedApkCount++;
                        }
                    }
                    // 查询是否有这台设备安装apk的日志
                    PushLogApk pushLogApk = pushLogApkMapper.selectOne(new LambdaQueryWrapper<PushLogApk>()
                            .eq(PushLogApk::getDeviceId, device.getId())
                            .eq(PushLogApk::getPushLogId, updatePushLog.getId()));
                    if (pushLogApk == null) {
                        pushLogApk = new PushLogApk();
                        pushLogApk.setPushLogId(updatePushLog.getId());
                        pushLogApk.setDeviceId(device.getId());
                        pushLogApk.setApkIds(installedApkIds);
                        pushLogApkMapper.insert(pushLogApk);
                    } else {
                        if (pushLogApk.getApkIds().equals(installedApkIds) || pushLogApk.getApkIds().length() > installedApkIds.length()) {
                            continue;
                        }
                        pushLogApk.setApkIds(installedApkIds);
                        pushLogApkMapper.updatePushLogApk(pushLogApk);
                    }
                } catch (Exception e) {
                    log.error("解析失败");
                }
            }
            if (updatePushLog.getInstalledApkCount() < installedApkCount) {
                updatePushLog.setInstalledApkCount(installedApkCount);
                updatePushLog.setCompletionPercentage(String.format("%.2f", (float) installedApkCount / tempPushLog.getInstallApkCount() * 100));
                updatePushLog.setRemainingCount(tempPushLog.getInstallApkCount() - installedApkCount);
                if (installedApkCount == tempPushLog.getInstallApkCount()) {
                    updatePushLog.setEndTime(new Date());
                }
            }
            pushLogMapper.updatePushLog(updatePushLog);
            tempPushLog.setInstalledApkCount(updatePushLog.getInstalledApkCount());
            tempPushLog.setCompletionPercentage(updatePushLog.getCompletionPercentage());
            tempPushLog.setRemainingCount(updatePushLog.getRemainingCount());
            tempPushLog.setEndTime(updatePushLog.getEndTime());
        }
        return pushLogList;
    }


    /**
     * 新增推送日志
     *
     * @param pushLogDTO 推送日志
     * @return 结果
     */
    @Override
    public int insertPushLog(PushLogDTO pushLogDTO) {
        if(pushLogDTO.getPushUser()==null){
            pushLogDTO.setPushUser(SecurityUtils.getUserId());
        }
        pushLogDTO.setStartTime(new Date());

        String[] deviceIdArray = pushLogDTO.getDeviceIds().split(",");
        // 查询已安装apk
        String apkIds = "";
        if (pushLogDTO.getGroupId() != null) {
            // 查询组apkIds
            List<GroupApk> groupApks = groupApkMapper.selectList(new LambdaQueryWrapper<GroupApk>().eq(GroupApk::getGroupId, pushLogDTO.getGroupId()));
            for (GroupApk groupApk : groupApks) {
                if (StringUtils.isEmpty(apkIds)) {
                    apkIds += groupApk.getApkId();

                } else {
                    apkIds += "," + groupApk.getApkId();
                }
            }
            pushLogDTO.setApkIds(apkIds);
        } else {
            apkIds = pushLogDTO.getApkIds();
        }

        List<Long> apkIdList = new ArrayList<>();
        for (String apkId : apkIds.split(",")) {
            apkIdList.add(Long.valueOf(apkId));
        }

        pushLogDTO.setInstallApkCount(apkIdList.size() * deviceIdArray.length);
        pushLogDTO.setInstalledApkCount(0);
        PushLog pushLog = new PushLog();
        BeanUtils.copyProperties(pushLogDTO, pushLog);

        pushLog.setCompletionPercentage("0%");
        pushLog.setRemainingCount(pushLogDTO.getInstallApkCount());

        return pushLogMapper.insert(pushLog);
    }

    /**
     * 修改推送日志
     *
     * @param pushLog 推送日志
     * @return 结果
     */
    @Override
    public int updatePushLog(PushLog pushLog) {
        return pushLogMapper.updatePushLog(pushLog);
    }

    /**
     * 批量删除推送日志
     *
     * @param ids 需要删除的推送日志主键
     * @return 结果
     */
    @Override
    public int deletePushLogByIds(Long[] ids) {
        return pushLogMapper.deletePushLogByIds(ids);
    }

    /**
     * 删除推送日志信息
     *
     * @param id 推送日志主键
     * @return 结果
     */
    @Override
    public int deletePushLogById(Long id) {
        return pushLogMapper.deletePushLogById(id);
    }

    @Override
    public List<DeviceVO> getDeviceByPushLogId(Long pushLogId) {
        PushLog pushLog = pushLogMapper.selectPushLogById(pushLogId);
        String[] deviceArray = pushLog.getDeviceIds().split(",");
        return deviceMapper.selectDeviceByDeviceIds(Arrays.asList(deviceArray));
    }

    @Override
    public List<Apk> getPushApkByPushLogId(Long pushLogId) {
        PushLog pushLog = pushLogMapper.selectPushLogById(pushLogId);
        String[] apkIdArray = pushLog.getApkIds().split(",");
        return apkMapper.selectApkByApkId(Arrays.asList(apkIdArray));
    }

    @Override
    public List<DeviceVO> getRemainingDeviceAndApk(Long pushLogId) {
        PushLog pushLog = pushLogMapper.selectPushLogById(pushLogId);

        String[] deviceIdArray = pushLog.getDeviceIds().split(",");
        List<Long> apkIdList = new ArrayList<>();
        List<DeviceVO> deviceVOList = new ArrayList<>();
        for (String apkId : pushLog.getApkIds().split(",")) {
            apkIdList.add(Long.valueOf(apkId));
        }
        deviceVOList = deviceMapper.selectDeviceByDeviceIds(Arrays.asList(deviceIdArray));
        List<DeviceVO> resultDeviceList = new ArrayList<>();

        for (DeviceVO deviceVO : deviceVOList) {
            HeartbeatLog heartbeatLog = heartbeatLogMapper.selectLastHeartbeatLogBySn(deviceVO.getSn());
            if (heartbeatLog == null) {
                continue;
            }
            String allPackage = heartbeatLog.getAllPackage();
            if (StringUtils.isEmpty(allPackage)) {
                continue;
            }
            // 查询是否有这台设备安装apk的日志
            PushLogApk pushLogApk = pushLogApkMapper.selectOne(new LambdaQueryWrapper<PushLogApk>()
                    .eq(PushLogApk::getDeviceId, deviceVO.getId())
                    .eq(PushLogApk::getPushLogId, pushLogId));
            if(pushLogApk == null || StringUtils.isEmpty(pushLogApk.getApkIds())){
                continue;
            }
            String[] installedApkIdArray = pushLogApk.getApkIds().split(",");
            List<Long> installedApkIdList = new ArrayList<>();
            for (String apkId : installedApkIdArray) {
                if(StringUtils.isEmpty(apkId)){
                    continue;
                }
                installedApkIdList.add(Long.valueOf(apkId));
            }

            try {
                ObjectMapper mapper = new ObjectMapper();
                PackageInfo[] packageInfoArray = mapper.readValue(allPackage, PackageInfo[].class);
                Map<Long, Apk> resApkIdMap = new HashMap<>();
                for (PackageInfo packageInfo : packageInfoArray) {
                    Apk apk = apkMapper.selectOne(new LambdaQueryWrapper<Apk>()
                            .eq(Apk::getApkPackage, packageInfo.getPackageName())
                            .eq(Apk::getVersion, packageInfo.getVersionName()));
                    if (apk != null) {
                        resApkIdMap.put(apk.getId(), apk);
                    }
                }
                ArrayList<Apk> apkArrayList = new ArrayList<>();
                for (Long apkId : apkIdList) {
                    if (!resApkIdMap.containsKey(apkId) && !installedApkIdList.contains(apkId)) {
                        if (deviceVO.getApkList() == null) {
                            apkArrayList.add(apkMapper.selectApkById(apkId));
                            deviceVO.setApkList(apkArrayList);
                            resultDeviceList.add(deviceVO);
                        } else {
                            deviceVO.getApkList().add(apkMapper.selectApkById(apkId));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析失败");
            }
        }
        return resultDeviceList;
    }
}