package com.sprt.pc.controller;

import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.mapper.OperationLogMapper;
import com.sprt.android.utils.MyWebSocketClient;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.pc.domain.Device;
import com.sprt.pc.dto.SendMessage;
import com.sprt.pc.dto.SendMessageInfo;
import com.sprt.pc.mapper.DeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.handshake.HandshakeImpl1Server;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.util.Date;

@Slf4j
@RestController
@RequestMapping("/pc/send")
public class SendToAndroid {

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @PostMapping("sendMessage")
    public AjaxResult sendMessage(@RequestBody SendMessage sendMessage) {
        log.info("接收数据：" + sendMessage);
        for (SendMessageInfo sendMessageInfo : sendMessage.getArray()) {
            threadPoolTaskExecutor.execute(() -> {
                Device device = deviceMapper.selectById(sendMessageInfo.getDeviceId());
                OperationLog operationLog = new OperationLog();
                operationLog.setType(sendMessageInfo.getType());
                operationLog.setSubdivisionType(sendMessageInfo.getSubdivisionType());
                operationLog.setIsSuccess("指令发送成功");
                operationLog.setDeviceId(sendMessageInfo.getDeviceId());
                operationLog.setSn(device.getSn());
                operationLog.setCreateTime(new Date());
                MyWebSocketClient client = null;
                operationLog.setDeviceId(sendMessageInfo.getDeviceId());
                try {
                    URI uri = new URI("ws://" + sendMessageInfo.getIp() + ":5008");
                    client = new MyWebSocketClient(uri);
                    client.connectBlocking();
                    client.onOpen(new HandshakeImpl1Server());

                    log.info("发送数据：" + sendMessageInfo.getContent());
                    client.send(sendMessageInfo.getContent());
                    operationLog.setContent("发送指令成功");
                    client.send(sendMessageInfo.getContent());
                    client.close();
                } catch (Exception e) {
                    log.error("连接异常", e);
                    operationLog.setIsSuccess("指令发送失败");
                    operationLog.setContent("发送指令失败，错误原因：" + e);
                    if (client != null) {
                        client.close();
                    }
                } finally {
                    operationLogMapper.insert(operationLog);
                }
            });
        }
        return AjaxResult.success("发送成功");
    }


}
