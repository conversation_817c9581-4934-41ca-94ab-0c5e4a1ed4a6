package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sprt.common.annotation.Excel;
#if($table.crud || $table.sub)
import com.sprt.common.core.domain.BaseEntity;
#elseif($table.tree)
import com.sprt.common.core.domain.TreeEntity;
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity")
#end
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("${tableName}")
public class ${ClassName} extends ${Entity}
        {
private static final long serialVersionUID=1L;

#foreach ($column in $columns)
    #if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
        #if($column.list)
            #set($parentheseIndex=$column.columnComment.indexOf("（"))
            #if($parentheseIndex != -1)
                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
            #else
                #set($comment=$column.columnComment)
            #end
            #if($parentheseIndex != -1)
            @Excel(name = "${comment}" , readConverterExp = "$column.readConverterExp()" )
            #elseif($column.javaType == 'Date')
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
            @Excel(name = "${comment}" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
            #else
            @Excel(name = "${comment}" )
            #end
        #end
        #if($column.isPk == 1)
        @TableId(value = "$column.columnName",type = IdType.ASSIGN_ID)
        #end
    private $column.javaType $column.javaField;

    #end
#end
#if($table.sub)
/** $table.subTable.functionName信息 */
private List<${subClassName}> ${subclassName}List;

#end
        }
