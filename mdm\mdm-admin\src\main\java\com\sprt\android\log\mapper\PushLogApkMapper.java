package com.sprt.android.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.android.log.domain.PushLogApk;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 推送日志设备上apkidMapper接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Repository
public interface PushLogApkMapper extends BaseMapper<PushLogApk> {
    /**
     * 查询推送日志设备上apkid
     *
     * @param id 推送日志设备上apkid主键
     * @return 推送日志设备上apkid
     */
    public PushLogApk selectPushLogApkById(Long id);

    /**
     * 查询推送日志设备上apkid列表
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 推送日志设备上apkid集合
     */
    public List<PushLogApk> selectPushLogApkList(PushLogApk pushLogApk);

    /**
     * 新增推送日志设备上apkid
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 结果
     */
    public int insertPushLogApk(PushLogApk pushLogApk);

    /**
     * 修改推送日志设备上apkid
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 结果
     */
    public int updatePushLogApk(PushLogApk pushLogApk);

    /**
     * 删除推送日志设备上apkid
     *
     * @param id 推送日志设备上apkid主键
     * @return 结果
     */
    public int deletePushLogApkById(Long id);

    /**
     * 批量删除推送日志设备上apkid
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePushLogApkByIds(Long[] ids);
}