package com.sprt.android.controller;

import com.sprt.android.domain.AndroidLoginBody;
import com.sprt.android.service.LoginService;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.pc.mapper.GroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("android/login")
public class LoginController extends BaseController {

    @Autowired
    private LoginService loginService;

    @PostMapping
    public AjaxResult login(@RequestBody AndroidLoginBody androidLoginBody) {
        return toAjax(loginService.login(androidLoginBody));
    }
}
