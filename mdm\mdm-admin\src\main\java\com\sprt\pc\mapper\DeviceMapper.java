package com.sprt.pc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.pc.domain.Device;
import com.sprt.pc.vo.DeviceVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Repository
public interface DeviceMapper extends BaseMapper<Device> {
    /**
     * 查询设备
     *
     * @param id 设备主键
     * @return 设备
     */
    public Device selectDeviceById(Long id);

    /**
     * 查询设备列表
     *
     * @param device 设备
     * @return 设备集合
     */
    public List<DeviceVO> selectDeviceList(Device device);

    /**
     * 新增设备
     *
     * @param device 设备
     * @return 结果
     */
    public int insertDevice(Device device);

    /**
     * 修改设备
     *
     * @param device 设备
     * @return 结果
     */
    public int updateDevice(Device device);

    /**
     * 删除设备
     *
     * @param id 设备主键
     * @return 结果
     */
    public int deleteDeviceById(Long id);

    /**
     * 批量删除设备
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] ids);

    /**
     * 通过sn修改设备信息
     *
     * @param ip
     * @param groupId
     * @param assetCode
     * @param sn
     * @param deviceId
     * @param date
     * @return
     */
    int updateDeviceBySn(@Param("mac") String mac, @Param("ip") String ip, @Param("groupId") Long groupId, @Param("assetCode") String assetCode, @Param("sn") String sn, @Param("deviceId") String deviceId, @Param("platform") String platform, @Param("version") String version, @Param("battery") String battery, @Param("rssi") String rssi, @Param("wifiName") String wifiName, @Param("status") Integer status, @Param("heartbeatTime") Date date, @Param("activityTime") String activityTime, @Param("remainingStorage") String remainingStorage, @Param("apMac") String apMac, @Param("isRecharge") String isRecharge, @Param("deviceName") String deviceName);

    /**
     * 通过分组id查询设备列表
     *
     * @param groupIds
     * @param where
     * @param status
     * @param battery
     * @return
     */
    List<DeviceVO> selectDeviceVOList(@Param("groupIds") List<Long> groupIds, @Param("where") String where, @Param("status") Integer status, @Param("battery") Integer battery);

    List<DeviceVO> selectDeviceListByMacs(@Param("macs") List<String> macs);

    List<DeviceVO> selectDeviceByDeviceIds(List<String> deviceIds);
}