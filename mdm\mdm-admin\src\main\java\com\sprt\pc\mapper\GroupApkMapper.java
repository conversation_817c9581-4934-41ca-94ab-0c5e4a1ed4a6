package com.sprt.pc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.pc.domain.GroupApk;
import com.sprt.pc.vo.GroupApkVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 组与apk绑定Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Repository
public interface GroupApkMapper extends BaseMapper<GroupApk> {
    /**
     * 查询组与apk绑定
     *
     * @param id 组与apk绑定主键
     * @return 组与apk绑定
     */
    public GroupApkVO selectGroupApkById(Long id);

    /**
     * 查询组与apk绑定列表
     *
     * @param groupApk 组与apk绑定
     * @return 组与apk绑定集合
     */
    public List<GroupApkVO> selectGroupApkList(GroupApk groupApk);

    /**
     * 新增组与apk绑定
     *
     * @param groupApk 组与apk绑定
     * @return 结果
     */
    public int insertGroupApk(GroupApk groupApk);

    /**
     * 修改组与apk绑定
     *
     * @param groupApk 组与apk绑定
     * @return 结果
     */
    public int updateGroupApk(GroupApk groupApk);

    /**
     * 删除组与apk绑定
     *
     * @param id 组与apk绑定主键
     * @return 结果
     */
    public int deleteGroupApkById(Long id);

    /**
     * 批量删除组与apk绑定
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGroupApkByIds(Long[] ids);
}