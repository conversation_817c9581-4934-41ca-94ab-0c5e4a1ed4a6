package com.sprt.pc.websocket;

import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class WebSocket extends TextWebSocketHandler {

    // 存储前端session与设备session的映射关系
    private final Map<String, WebSocketSession> frontEndSessions = new ConcurrentHashMap<>();
    private final Map<String, WebSocketSession> deviceSessions = new ConcurrentHashMap<>();
    private final Map<String, String> sessionMapping = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 前端连接建立时，session中暂不关联设备
        frontEndSessions.put(session.getId(), session);
    }

    @Override
    public void handleTextMessage(WebSocketSession frontEndSession, TextMessage message) throws Exception {
        String payload = message.getPayload();

        // 如果这是前端发来的第一条消息，包含deviceIp
        if (!sessionMapping.containsKey(frontEndSession.getId())) {
            // 解析deviceIp（假设消息格式为JSON或简单文本）
            String deviceIp = parseDeviceIp(payload);
            if (deviceIp != null) {
                // 建立到设备的WebSocket连接
                connectToDevice(frontEndSession, deviceIp);
            }
        } else {
            // 转发消息到设备
            WebSocketSession deviceSession = deviceSessions.get(sessionMapping.get(frontEndSession.getId()));
            if (deviceSession != null && deviceSession.isOpen()) {
                deviceSession.sendMessage(new TextMessage(payload));
            }
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        handleClose(session);
    }

    /**
     * 解析消息中的deviceIp
     */
    private String parseDeviceIp(String message) {
        // 这里根据实际的消息格式进行解析
        // 示例：如果消息是 {"deviceIp": "*************", "otherData": "..."}
        if (message.contains("deviceIp")) {
            // 简单的字符串解析示例
            int start = message.indexOf("\"deviceIp\"") + 12;
            int end = message.indexOf("\"", start);
            if (start > 11 && end > start) {
                return message.substring(start, end);
            }
        }
        return null;
    }

    /**
     * 连接到设备的WebSocket
     */
    private void connectToDevice(WebSocketSession frontEndSession, String deviceIp) throws Exception {
        // 创建设备WebSocket客户端处理器
        DeviceWebSocketHandler deviceHandler = new DeviceWebSocketHandler(frontEndSession);

        // 构建设备WebSocket URI（根据实际协议调整）
        URI deviceUri = new URI("ws://" + deviceIp);

        // 这里需要使用WebSocket客户端连接设备（示例代码，实际需要根据你的WebSocket客户端实现）
        // 例如使用Spring WebSocket客户端：
        WebSocketClient client = new StandardWebSocketClient();
        WebSocketSession deviceSession = client.doHandshake(deviceHandler, String.valueOf(deviceUri)).get();

        // 建立映射关系
        sessionMapping.put(frontEndSession.getId(), deviceSession.getId());
        deviceSessions.put(deviceSession.getId(), deviceSession);
    }

    /**
     * 处理连接关闭
     */
    private void handleClose(WebSocketSession session) throws IOException {
        String sessionId = session.getId();

        // 如果是前端连接关闭
        if (frontEndSessions.containsKey(sessionId)) {
            frontEndSessions.remove(sessionId);

            // 关闭对应的设备连接
            String deviceSessionId = sessionMapping.get(sessionId);
            if (deviceSessionId != null) {
                WebSocketSession deviceSession = deviceSessions.get(deviceSessionId);
                if (deviceSession != null && deviceSession.isOpen()) {
                    deviceSession.close();
                }
                deviceSessions.remove(deviceSessionId);
                sessionMapping.remove(sessionId);
            }
        }
        // 如果是设备连接关闭
        else if (deviceSessions.containsKey(sessionId)) {
            deviceSessions.remove(sessionId);

            // 找到对应的前端连接并关闭
            String frontEndSessionId = null;
            for (Map.Entry<String, String> entry : sessionMapping.entrySet()) {
                if (entry.getValue().equals(sessionId)) {
                    frontEndSessionId = entry.getKey();
                    break;
                }
            }

            if (frontEndSessionId != null) {
                WebSocketSession frontEndSession = frontEndSessions.get(frontEndSessionId);
                if (frontEndSession != null && frontEndSession.isOpen()) {
                    frontEndSession.close();
                }
                frontEndSessions.remove(frontEndSessionId);
                sessionMapping.remove(frontEndSessionId);
            }
        }
    }

    /**
     * 设备WebSocket处理器
     */
    private class DeviceWebSocketHandler extends TextWebSocketHandler {
        private final WebSocketSession frontEndSession;

        public DeviceWebSocketHandler(WebSocketSession frontEndSession) {
            this.frontEndSession = frontEndSession;
        }

        @Override
        public void afterConnectionEstablished(WebSocketSession deviceSession) throws Exception {
            // 设备连接建立后，建立映射关系
            sessionMapping.put(frontEndSession.getId(), deviceSession.getId());
            deviceSessions.put(deviceSession.getId(), deviceSession);
        }

        @Override
        public void handleTextMessage(WebSocketSession deviceSession, TextMessage message) throws Exception {
            // 将设备消息转发给前端
            if (frontEndSession.isOpen()) {
                frontEndSession.sendMessage(new TextMessage(message.getPayload()));
            }
        }

        @Override
        public void afterConnectionClosed(WebSocketSession deviceSession, CloseStatus status) throws Exception {
            handleClose(deviceSession);
        }
    }
}
