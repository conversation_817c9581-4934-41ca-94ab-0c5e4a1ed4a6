package com.sprt.android.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.vo.OperationLogVO;

import java.util.List;

/**
 * 操作日志Service接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface OperationLogService extends IService<OperationLog> {
    /**
     * 查询操作日志
     *
     * @param id 操作日志主键
     * @return 操作日志
     */
    public OperationLog selectOperationLogById(Long id);

    /**
     * 查询操作日志列表
     *
     * @param operationLog 操作日志
     * @return 操作日志集合
     */
    public List<OperationLogVO> selectOperationLogList(OperationLog operationLog);

    /**
     * 新增操作日志
     *
     * @param operationLog 操作日志
     * @return 结果
     */
    public int insertOperationLog(OperationLog operationLog);

    /**
     * 修改操作日志
     *
     * @param operationLog 操作日志
     * @return 结果
     */
    public int updateOperationLog(OperationLog operationLog);

    /**
     * 批量删除操作日志
     *
     * @param ids 需要删除的操作日志主键集合
     * @return 结果
     */
    public int deleteOperationLogByIds(Long[] ids);

    /**
     * 删除操作日志信息
     *
     * @param id 操作日志主键
     * @return 结果
     */
    public int deleteOperationLogById(Long id);

    List<OperationLogVO> getOperationLogByDeviceId(Long deviceId);
}