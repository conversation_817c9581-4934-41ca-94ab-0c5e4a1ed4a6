package com.sprt.android.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.vo.OperationLogVO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Repository
public interface OperationLogMapper extends BaseMapper<OperationLog> {
    /**
     * 查询操作日志
     *
     * @param id 操作日志主键
     * @return 操作日志
     */
    public OperationLog selectOperationLogById(Long id);

    /**
     * 查询操作日志列表
     *
     * @param operationLog 操作日志
     * @return 操作日志集合
     */
    public List<OperationLogVO> selectOperationLogList(OperationLog operationLog);

    /**
     * 新增操作日志
     *
     * @param operationLog 操作日志
     * @return 结果
     */
    public int insertOperationLog(OperationLog operationLog);

    /**
     * 修改操作日志
     *
     * @param operationLog 操作日志
     * @return 结果
     */
    public int updateOperationLog(OperationLog operationLog);

    /**
     * 删除操作日志
     *
     * @param id 操作日志主键
     * @return 结果
     */
    public int deleteOperationLogById(Long id);

    /**
     * 批量删除操作日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOperationLogByIds(Long[] ids);

    /**
     * 根据设备id查询操作日志
     * @param deviceId
     * @return
     */
    List<OperationLogVO> selectOperationLogByDeviceId(Long deviceId);
}