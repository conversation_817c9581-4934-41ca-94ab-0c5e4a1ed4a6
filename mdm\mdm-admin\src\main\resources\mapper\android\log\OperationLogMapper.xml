<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.android.log.mapper.OperationLogMapper">

    <resultMap type="OperationLog" id="OperationLogResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="subdivisionType"    column="subdivision_type"    />
        <result property="isSuccess"    column="is_success"    />
        <result property="deviceId"    column="device_id"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="sn"    column="sn"    />
    </resultMap>

    <sql id="selectOperationLogVo">
        select id, type, subdivision_type, is_success, device_id, content, create_time, sn from tb_operation_log
    </sql>

    <select id="selectOperationLogList" parameterType="OperationLog" resultType="com.sprt.android.log.vo.OperationLogVO">
        select t.id, t.type, t.subdivision_type, t.is_success, t.device_id, t.content, t.create_time, d.mac, g.group_name, d.ip, d.device_name from tb_operation_log  t
        left join tb_device d on t.device_id = d.id
        left join tb_group g on d.group_id = g.id
        <where>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="subdivisionType != null  and subdivisionType != ''"> and subdivision_type = #{subdivisionType}</if>
            <if test="isSuccess != null  and isSuccess != ''"> and is_success = #{isSuccess}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectOperationLogById" parameterType="Long" resultMap="OperationLogResult">
        <include refid="selectOperationLogVo"/>
        where id = #{id}
    </select>
    <select id="selectOperationLogByDeviceId" resultType="com.sprt.android.log.vo.OperationLogVO">
        select t.id, t.type, t.subdivision_type, t.is_success, t.device_id, t.content, t.create_time, d.mac, g.group_name, d.ip, d.device_name from tb_operation_log  t
        left join tb_device d on t.device_id = d.id
        left join tb_group g on d.group_id = g.id
        where t.device_id = #{deviceId}
        order by t.create_time desc
    </select>

    <insert id="insertOperationLog" parameterType="OperationLog">
        insert into tb_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="subdivisionType != null">subdivision_type,</if>
            <if test="isSuccess != null">is_success,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="sn != null">sn,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="subdivisionType != null">#{subdivisionType},</if>
            <if test="isSuccess != null">#{isSuccess},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="sn != null">#{sn},</if>
        </trim>
    </insert>

    <update id="updateOperationLog" parameterType="OperationLog">
        update tb_operation_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="subdivisionType != null">subdivision_type = #{subdivisionType},</if>
            <if test="isSuccess != null">is_success = #{isSuccess},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="sn != null">sn = #{sn},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOperationLogById" parameterType="Long">
        delete from tb_operation_log where id = #{id}
    </delete>

    <delete id="deleteOperationLogByIds" parameterType="String">
        delete from tb_operation_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>