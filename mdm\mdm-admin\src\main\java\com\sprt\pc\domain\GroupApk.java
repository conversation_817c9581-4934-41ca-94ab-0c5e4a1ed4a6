package com.sprt.pc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组与apk绑定对象 tb_group_apk
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_group_apk")
public class GroupApk {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 组id
     */
    @Excel(name = "组id")
    private Long groupId;

    /**
     * apkid
     */
    @Excel(name = "apkid")
    private Long apkId;

}
