package com.sprt.pc.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class ProfileHashUtil {

    public static String getProfileHash(String profile) {
        try {
            // 1. 转为 UTF-8 字节
            byte[] profileBytes = profile.getBytes(StandardCharsets.UTF_8);

            // 2. SHA-256 哈希计算
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(profileBytes);

            // 3. Base64 编码（RFC 4648 标准）
            return Base64.getEncoder().encodeToString(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }

    public static void main(String[] args) {
        String profile = "IV-PUSH-DATA-BACKEND";
        String profileHash = getProfileHash(profile);
        System.out.println("profileHash: " + profileHash);
        System.out.println("87CJVGJDrtUerpP86AQD8sHy6i5EG9Xn7dyZjEuUboQ=".equals(profileHash));
    }
}
