package com.sprt.android.log.controller;

import com.sprt.android.log.domain.PushLog;
import com.sprt.android.log.dto.PushLogDTO;
import com.sprt.android.log.service.PushLogService;
import com.sprt.android.log.vo.PushLogVO;
import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 推送日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/android/log")
public class PushLogController extends BaseController
{
    @Autowired
    private PushLogService pushLogService;

    /**
     * 查询推送日志列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PushLog pushLog)
    {
        startPage();
        List<PushLogVO> list = pushLogService.selectPushLogList(pushLog);
        return getDataTable(list);
    }

    /**
     * 导出推送日志列表
     */
    @Log(title = "推送日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PushLog pushLog)
    {
        List<PushLogVO> list = pushLogService.selectPushLogList(pushLog);
        ExcelUtil<PushLogVO> util = new ExcelUtil<PushLogVO>(PushLogVO.class);
        util.exportExcel(response, list, "推送日志数据");
    }

    /**
     * 获取推送日志详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(pushLogService.selectPushLogById(id));
    }

    /**
     * 新增推送日志
     */
    @Log(title = "推送日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PushLogDTO pushLogDTO)
    {
        return toAjax(pushLogService.insertPushLog(pushLogDTO));
    }

    /**
     * 修改推送日志
     */
    @Log(title = "推送日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PushLog pushLog)
    {
        return toAjax(pushLogService.updatePushLog(pushLog));
    }

    /**
     * 删除推送日志
     */
    @Log(title = "推送日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(pushLogService.deletePushLogByIds(ids));
    }

    @GetMapping("getDeviceByPushLogId/{pushLogId}")
    public AjaxResult getDeviceByPushLogId(@PathVariable Long pushLogId)
    {
        return success(pushLogService.getDeviceByPushLogId(pushLogId));
    }

    @GetMapping("getPushApkByPushLogId/{pushLogId}")
    public AjaxResult getPushApkByPushLogId(@PathVariable Long pushLogId)
    {
        return success(pushLogService.getPushApkByPushLogId(pushLogId));
    }

    @GetMapping("getRemainingDeviceAndApk/{pushLogId}")
    public AjaxResult getRemainingDeviceAndApk(@PathVariable Long pushLogId)
    {
        return success(pushLogService.getRemainingDeviceAndApk(pushLogId));
    }
}
