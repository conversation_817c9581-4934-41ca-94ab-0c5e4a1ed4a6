package com.sprt.pc.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;

@Component
public class ServerInfoUtil {

    @Value("${server.port}")
    private int serverPort;

    private static int port;
    private static String ipAddress;

    @PostConstruct
    public void init() {
        port = serverPort;
        try {
            InetAddress inetAddress = InetAddress.getLocalHost();
            ipAddress = inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            ipAddress = "127.0.0.1"; // 获取失败就默认 localhost
        }
    }

    public static String getIpAddress() {
        return ipAddress;
    }

    public static int getPort() {
        return port;
    }

    public static String getServerUrl() {
        return "http://" + ipAddress + ":" + port;
    }
}
