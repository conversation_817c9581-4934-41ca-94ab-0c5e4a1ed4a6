package com.sprt.android.log.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 推送日志对象 tb_push_log
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PushLogVO {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 推送人
     */
    @Excel(name = "推送人")
    private String nickName;

    /**
     * 设备所有id
     */
    @Excel(name = "设备所有id")
    private String deviceIds;

    /**
     * 所有apkid
     */
    @Excel(name = "所有apkid")
    private String apkIds;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 应安装数
     */
    @Excel(name = "应安装数")
    private Integer installApkCount;

    /**
     * 实际安装数
     */
    @Excel(name = "实际安装数")
    private Integer installedApkCount;

    /**
     * 任务完成量
     */
    @Excel(name = "任务完成量")
    private String completionPercentage;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 剩余apk数
     */
    @Excel(name = "剩余apk数")
    private Integer remainingCount;

}
