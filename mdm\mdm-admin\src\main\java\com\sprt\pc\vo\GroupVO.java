package com.sprt.pc.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sprt.common.annotation.Excel;
import com.sprt.pc.domain.Apk;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分组对象 tb_group
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_group")
public class GroupVO
{

    /** 主键 */
    private Long id;

    /** 组名 */
    @Excel(name = "组名" )
    private String groupName;

    /** 排序 */
    @Excel(name = "排序" )
    private Integer sort;

    /** 父级id */
    @Excel(name = "父级id" )
    private Long parentId;

    /** 所有祖籍 */
    @Excel(name = "所有祖籍" )
    private String parentIds;

    /** 配置apk列表 */
    @Excel(name = "配置apk列表" )
    private List<Apk> apkList;

    /** 账号 */
    @Excel(name = "账号" )
    private String userName;

    /** 密码 */
    @Excel(name = "密码" )
    private String password;

    /** 描述 */
    @Excel(name = "描述" )
    private String describe;

}
