package com.sprt.android.log.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sprt.android.log.mapper.PushLogApkMapper;
import com.sprt.android.log.domain.PushLogApk;
import com.sprt.android.log.service.PushLogApkService;

/**
 * 推送日志设备上apkidService业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class PushLogApkServiceImpl extends ServiceImpl<PushLogApkMapper, PushLogApk> implements PushLogApkService {
    @Autowired
    private PushLogApkMapper pushLogApkMapper;

    /**
     * 查询推送日志设备上apkid
     *
     * @param id 推送日志设备上apkid主键
     * @return 推送日志设备上apkid
     */
    @Override
    public PushLogApk selectPushLogApkById(Long id) {
        return pushLogApkMapper.selectPushLogApkById(id);
    }

    /**
     * 查询推送日志设备上apkid列表
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 推送日志设备上apkid
     */
    @Override
    public List<PushLogApk> selectPushLogApkList(PushLogApk pushLogApk) {
        return pushLogApkMapper.selectPushLogApkList(pushLogApk);
    }

    /**
     * 新增推送日志设备上apkid
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 结果
     */
    @Override
    public int insertPushLogApk(PushLogApk pushLogApk) {
            return pushLogApkMapper.insertPushLogApk(pushLogApk);
    }

    /**
     * 修改推送日志设备上apkid
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 结果
     */
    @Override
    public int updatePushLogApk(PushLogApk pushLogApk) {
        return pushLogApkMapper.updatePushLogApk(pushLogApk);
    }

    /**
     * 批量删除推送日志设备上apkid
     *
     * @param ids 需要删除的推送日志设备上apkid主键
     * @return 结果
     */
    @Override
    public int deletePushLogApkByIds(Long[] ids) {
        return pushLogApkMapper.deletePushLogApkByIds(ids);
    }

    /**
     * 删除推送日志设备上apkid信息
     *
     * @param id 推送日志设备上apkid主键
     * @return 结果
     */
    @Override
    public int deletePushLogApkById(Long id) {
        return pushLogApkMapper.deletePushLogApkById(id);
    }
}