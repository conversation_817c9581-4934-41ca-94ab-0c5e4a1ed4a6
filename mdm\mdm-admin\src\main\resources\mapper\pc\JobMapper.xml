<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.pc.mapper.JobMapper">

    <resultMap type="Job" id="JobResult">
        <result property="id" column="id"/>
        <result property="pushTime" column="push_time"/>
        <result property="deviceIds" column="device_ids"/>
        <result property="apkIds" column="apk_ids"/>
        <result property="checkedPackage" column="checked_package"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="isEnable" column="is_enable"/>
    </resultMap>

    <sql id="selectJobVo">
        select j.id,
               j.push_time,
               j.device_ids,
               j.apk_ids,
               j.checked_package,
               j.status,
               j.remark,
               u.nick_name,
               j.create_time,
               j.is_enable
        from tb_job j
                 left join sys_user u on j.create_by = u.user_id
    </sql>

    <select id="selectJobList" parameterType="Job" resultType="com.sprt.android.vo.JobVO">
        <include refid="selectJobVo"/>
        <where>
            <if test="pushTime != null ">and j.push_time = #{pushTime}</if>
            <if test="apkIds != null  and apkIds != ''">and j.apk_ids = #{apkIds}</if>
            <if test="checkedPackage != null ">and j.checked_package = #{checkedPackage}</if>
            <if test="status != null ">and j.status = #{status}</if>
            <if test="isEnable != null ">and j.is_enable = #{isEnable}</if>
        </where>
        order by j.status asc
    </select>

    <select id="selectJobById" parameterType="Long" resultType="com.sprt.android.vo.JobVO">
        <include refid="selectJobVo"/>
        where id = #{id}
    </select>

    <insert id="insertJob" parameterType="Job">
        insert into tb_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="deviceIds != null">device_ids,</if>
            <if test="apkIds != null">apk_ids,</if>
            <if test="checkedPackage != null">checked_package,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="isEnable != null">is_enable,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="deviceIds != null">#{deviceIds},</if>
            <if test="apkIds != null">#{apkIds},</if>
            <if test="checkedPackage != null">#{checkedPackage},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="isEnable != null">#{isEnable},</if>
        </trim>
    </insert>

    <update id="updateJob" parameterType="Job">
        update tb_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="deviceIds != null">device_ids = #{deviceIds},</if>
            <if test="apkIds != null">apk_ids = #{apkIds},</if>
            <if test="checkedPackage != null">checked_package = #{checkedPackage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJobById" parameterType="Long">
        delete
        from tb_job
        where id = #{id}
    </delete>

    <delete id="deleteJobByIds" parameterType="String">
        delete from tb_job where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>