package com.sprt.android.log.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 操作日志对象 tb_operation_log
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperationLogVO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 操作类型
     */
    @Excel(name = "操作类型")
    private String type;

    /**
     * 日志细分
     */
    @Excel(name = "日志细分")
    private String subdivisionType;

    /**
     * 是否成功
     */
    @Excel(name = "是否成功")
    private String isSuccess;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 日志内容
     */
    @Excel(name = "日志内容")
    private String content;

    @Excel(name = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Excel(name = "mac")
    private String mac;

    @Excel(name = "分组")
    private String groupName;

    @Excel(name = "ip")
    private String ip;

    @Excel(name = "设备名称")
    private String deviceName;

    @Excel(name = "sn")
    private String sn;
}
