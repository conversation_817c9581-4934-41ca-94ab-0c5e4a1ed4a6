package com.sprt.android.log.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sprt.android.log.domain.HeartbeatLog;
import com.sprt.android.log.dto.PackageInfo;
import com.sprt.android.log.mapper.HeartbeatLogMapper;
import com.sprt.android.log.service.HeartbeatLogService;
import com.sprt.common.utils.DateUtils;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.mapper.ApkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 心跳日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class HeartbeatLogServiceImpl extends ServiceImpl<HeartbeatLogMapper, HeartbeatLog> implements HeartbeatLogService {
    @Autowired
    private HeartbeatLogMapper heartbeatLogMapper;

    @Autowired
    private ApkMapper apkMapper;

    /**
     * 查询心跳日志
     *
     * @param id 心跳日志主键
     * @return 心跳日志
     */
    @Override
    public HeartbeatLog selectHeartbeatLogById(Long id) {
        return heartbeatLogMapper.selectHeartbeatLogById(id);
    }

    /**
     * 查询心跳日志列表
     *
     * @param heartbeatLog 心跳日志
     * @return 心跳日志
     */
    @Override
    public List<HeartbeatLog> selectHeartbeatLogList(HeartbeatLog heartbeatLog) {
        return heartbeatLogMapper.selectHeartbeatLogList(heartbeatLog);
    }

    /**
     * 新增心跳日志
     *
     * @param heartbeatLog 心跳日志
     * @return 结果
     */
    @Override
    public int insertHeartbeatLog(HeartbeatLog heartbeatLog) {
        heartbeatLog.setCreateTime(DateUtils.getNowDate());
        return heartbeatLogMapper.insertHeartbeatLog(heartbeatLog);
    }

    /**
     * 修改心跳日志
     *
     * @param heartbeatLog 心跳日志
     * @return 结果
     */
    @Override
    public int updateHeartbeatLog(HeartbeatLog heartbeatLog) {
        return heartbeatLogMapper.updateHeartbeatLog(heartbeatLog);
    }

    /**
     * 批量删除心跳日志
     *
     * @param ids 需要删除的心跳日志主键
     * @return 结果
     */
    @Override
    public int deleteHeartbeatLogByIds(Long[] ids) {
        return heartbeatLogMapper.deleteHeartbeatLogByIds(ids);
    }

    /**
     * 删除心跳日志信息
     *
     * @param id 心跳日志主键
     * @return 结果
     */
    @Override
    public int deleteHeartbeatLogById(Long id) {
        return heartbeatLogMapper.deleteHeartbeatLogById(id);
    }

    @Override
    public void selectApkCount() {
        // 查询所有apk
        List<Apk> apkList = apkMapper.selectList(null);
        Map<String, Integer> apkCountMap = new HashMap<>();
        for (Apk apk : apkList) {
            apkCountMap.put(apk.getApkPackage() + "," + apk.getVersion(), 0);
        }
        // 查找所有设备最近的一次心跳
        List<HeartbeatLog> heartbeatLogList = heartbeatLogMapper.selectLastHeartbeatLog();

        List<PackageInfo> packageInfoList = new ArrayList<>();
        for (HeartbeatLog heartbeatLog : heartbeatLogList) {
            String allPackage = heartbeatLog.getAllPackage();
            if (StringUtils.isEmpty(allPackage)) {
                continue;
            }
            try {
                ObjectMapper mapper = new ObjectMapper();
                PackageInfo[] array = mapper.readValue(allPackage, PackageInfo[].class);
                packageInfoList.addAll(Arrays.asList(array));
            } catch (Exception e) {
                log.error("解析失败");
            }
        }
        for (PackageInfo packageInfo : packageInfoList) {
            String packageName = packageInfo.getPackageName();
            String versionName = packageInfo.getVersionName();
            if (apkCountMap.get(packageName + "," + versionName) != null) {
                apkCountMap.put(packageName + "," + versionName, apkCountMap.get(packageName + "," + versionName) + 1);
            }
        }

        for (Apk apk : apkList) {
            Integer integer = apkCountMap.get(apk.getApkPackage() + "," + apk.getVersion());
            apk.setNum((long) (integer == null ? 0 : integer));
            apkMapper.updateApk(apk);
        }
    }
}