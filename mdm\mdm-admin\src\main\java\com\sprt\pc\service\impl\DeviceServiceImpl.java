package com.sprt.pc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sprt.android.log.domain.HeartbeatLog;
import com.sprt.android.log.dto.PackageInfo;
import com.sprt.android.log.mapper.HeartbeatLogMapper;
import com.sprt.android.utils.DeviceIdUtil;
import com.sprt.code_analysis.utils.DecryptUtil;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.ActivityCode;
import com.sprt.pc.domain.Device;
import com.sprt.pc.domain.Group;
import com.sprt.pc.dto.DeviceDTO;
import com.sprt.pc.mapper.ActivityCodeMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupMapper;
import com.sprt.pc.service.DeviceService;
import com.sprt.pc.vo.DeviceVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {
    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private ActivityCodeMapper activityCodeMapper;

    @Autowired
    private HeartbeatLogMapper heartbeatLogMapper;

    /**
     * 查询设备
     *
     * @param id 设备主键
     * @return 设备
     */
    @Override
    public Device selectDeviceById(Long id) {
        return deviceMapper.selectDeviceById(id);
    }

    /**
     * 查询设备列表
     *
     * @param deviceDTO 设备
     * @return 设备
     */
    @Override
    @Transactional
    public List<DeviceVO> selectDeviceList(DeviceDTO deviceDTO) {
        Device device = new Device();
        BeanUtils.copyProperties(deviceDTO, device);
        Group group = new Group();

        // 获取分组信息
        if (deviceDTO.getGroupId() == null) {
            List<Group> groupList = groupMapper.selectList(new LambdaQueryWrapper<Group>().eq(Group::getParentIds, 0));
            deviceDTO.setGroupId(groupList.get(0).getId());
            group = groupList.get(0);
        } else {
            group = groupMapper.selectGroupById(deviceDTO.getGroupId());
        }

        // 查找子分组
        if (group == null) {
            throw new RuntimeException("分组ID不存在");
        }

        String tempParentIds = group.getParentIds() + "," + group.getId();
        List<Group> groupList = groupMapper.selectList(new LambdaQueryWrapper<Group>().like(Group::getParentIds, tempParentIds));
        List<Long> groupIds = new ArrayList<>();
        groupIds.add(deviceDTO.getGroupId());
        for (Group tempGroup : groupList) {
            groupIds.add(tempGroup.getId());
        }

        // 处理ActivityCode解密
        long codeSum = processActivityCodes();

        // 批量查询所有设备
        List<Device> devices = deviceMapper.selectList(null);

        // 更新设备激活状态
        updateDeviceActivationStatus(devices, codeSum);

        // 获取设备列表
        List<DeviceVO> deviceVOList = fetchDeviceVOList(deviceDTO, groupIds);

        // 批量查询设备活动时间
        updateDeviceActivityTime(deviceVOList);

        return deviceVOList;
    }

    private long processActivityCodes() {
        long codeSum = 0L;
        try {
            List<ActivityCode> list = activityCodeMapper.selectList(new LambdaQueryWrapper<ActivityCode>().eq(ActivityCode::getType, 0));
            for (ActivityCode code : list) {
                Map<String, String> stringObjectMap = DecryptUtil.decryptDaysAndDeviceId(code.getCode());
                String days = stringObjectMap.get("days");
                String deviceId = stringObjectMap.get("deviceId");
                String deviceSum = stringObjectMap.get("deviceNum");

                if (DeviceIdUtil.generateDeviceId().equals(deviceId)) {
                    if (StringUtils.isEmpty(code.getExpireTime()) || StringUtils.isEmpty(code.getDeviceSum())) {
                        throw new RuntimeException("激活码信息不完整");
                    }
                    if (days.equals(code.getExpireTime()) && code.getDeviceSum().equals(deviceSum)) {
                        codeSum += Long.parseLong(code.getDeviceSum());
                    } else {
                        codeSum += Long.parseLong(deviceSum);
                        // 更新ActivityCode
                        ActivityCode activityCode = new ActivityCode();
                        BeanUtils.copyProperties(code, activityCode);
                        activityCode.setExpireTime(days);
                        activityCode.setDeviceSum(deviceSum);
                        if (Long.parseLong(days) < System.currentTimeMillis()) {
                            activityCode.setType(1);
                        }
                        activityCodeMapper.updateActivityCode(activityCode);
                    }
                } else {
                    throw new RuntimeException("激活码无效");
                }
            }
        } catch (Exception e) {
            log.error("查询注册码失败", e);
        }
        return codeSum;
    }

    private void updateDeviceActivationStatus(List<Device> devices, long codeSum) {
        int activitySum = 0;
        for (Device item : devices) {
            if (item.getStatus() == 1 || item.getStatus() == 0) {
                activitySum++;
            }
            if (item.getStatus() == 0) {
                item.setWifiName("");
                item.setBattery("");
                item.setRssi("");
                item.setApMac("");
                item.setIsRecharge("");
            }
        }
        this.updateBatchById(devices); // 批量更新设备信息

        // 根据codeSum更新设备状态
        if (activitySum != codeSum) {
            for (Device item : devices) {
                if (codeSum > 0) {
                    codeSum--;
                    if (item.getStatus() == 2) {
                        item.setStatus(1);
                        this.updateBatchById(devices); // 批量更新设备状态
                    }
                } else {
                    if (item.getStatus() != 2) {
                        item.setStatus(2);
                    }
                }
            }
        }
    }

    private List<DeviceVO> fetchDeviceVOList(DeviceDTO deviceDTO, List<Long> groupIds) {
        List<DeviceVO> deviceVOList;
        if (StringUtils.isEmpty(deviceDTO.getWhere())) {
            deviceVOList = deviceMapper.selectDeviceVOList(groupIds, null, deviceDTO.getStatus(), deviceDTO.getBattery());
        } else {
            deviceVOList = deviceMapper.selectDeviceVOList(groupIds, deviceDTO.getWhere(), deviceDTO.getStatus(), deviceDTO.getBattery());
        }
        return deviceVOList;
    }

    private void updateDeviceActivityTime(List<DeviceVO> deviceVOList) {
        for (DeviceVO deviceVO : deviceVOList) {
            int count = Math.toIntExact(heartbeatLogMapper.selectCount(new LambdaQueryWrapper<HeartbeatLog>()
                    .eq(HeartbeatLog::getSn, deviceVO.getSn())
                    .eq(HeartbeatLog::getScreenState, 1)
                    .ge(HeartbeatLog::getCreateTime, LocalDate.now().atStartOfDay())));
            deviceVO.setActivityTime(Integer.toString(count));
        }
    }


    /**
     * 新增设备
     *
     * @param device 设备
     * @return 结果
     */
    @Override
    public int insertDevice(Device device) {
        device.setHeartbeatTime(new Date());
        return deviceMapper.insertDevice(device);
    }

    /**
     * 修改设备
     *
     * @param device 设备
     * @return 结果
     */
    @Override
    public int updateDevice(Device device) {
        return deviceMapper.updateDevice(device);
    }

    /**
     * 批量删除设备
     *
     * @param ids 需要删除的设备主键
     * @return 结果
     */
    @Override
    public int deleteDeviceByIds(Long[] ids) {
        return deviceMapper.deleteDeviceByIds(ids);
    }

    /**
     * 删除设备信息
     *
     * @param id 设备主键
     * @return 结果
     */
    @Override
    public int deleteDeviceById(Long id) {
        return deviceMapper.deleteDeviceById(id);
    }

    @Override
    public List<DeviceVO> selectInstallDevice(String packageName, String versionName) {
        List<String> macs = new ArrayList<>();

        // 查找所有设备最近的一次心跳
        List<HeartbeatLog> heartbeatLogList = heartbeatLogMapper.selectLastHeartbeatLog();

        for (HeartbeatLog heartbeatLog : heartbeatLogList) {
            String allPackage = heartbeatLog.getAllPackage();
            if (StringUtils.isEmpty(allPackage)) {
                continue;
            }

            try {
                ObjectMapper mapper = new ObjectMapper();
                PackageInfo[] array = mapper.readValue(allPackage, PackageInfo[].class);
                for (PackageInfo packageInfo : array) {
                    String tempPackageName = packageInfo.getPackageName();
                    String tempVersionName = packageInfo.getVersionName();
                    if(packageName.equals(tempPackageName)&&versionName.equals(tempVersionName)){
                        macs.add(heartbeatLog.getMac());
                    }
                }
            } catch (Exception e) {
                log.error("解析失败");
            }
        }
        return deviceMapper.selectDeviceListByMacs(macs);
    }

    @Override
    public List<DeviceVO> getDeviceByDeviceIds(List<String> deviceIds) {
        return deviceMapper.selectDeviceByDeviceIds(deviceIds);
    }
}