package com.sprt.pc.websocket;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig2 implements WebSocketConfigurer {

    private final WebSocket webSocket;
    private final WebSocketProxyHandler webSocketProxyHandler;

    public WebSocketConfig2(WebSocket webSocket, WebSocketProxyHandler webSocketProxyHandler) {
        this.webSocket = webSocket;
        this.webSocketProxyHandler = webSocketProxyHandler;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 注册WebSocket代理处理器
        registry.addHandler(webSocketProxyHandler, "/proxy-websocket")
                .setAllowedOrigins("*")
                .withSockJS(); // 添加SockJS支持以提高兼容性

        // 如果需要，也可以注册原有的WebSocket处理器到不同路径
        // registry.addHandler(webSocket, "/device-websocket")
        //         .setAllowedOrigins("*");
    }
}
