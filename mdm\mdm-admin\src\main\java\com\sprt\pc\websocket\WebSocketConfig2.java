package com.sprt.pc.websocket;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig2 implements WebSocketConfigurer {
    
    private final WebSocket webSocket;
    
    public WebSocketConfig2(WebSocket webSocket) {
        this.webSocket = webSocket;
    }
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new WebSocketProxyHandler(), "/proxy-websocket")
                .setAllowedOrigins("*");
    }
}
