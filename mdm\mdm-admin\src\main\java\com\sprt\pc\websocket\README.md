# WebSocket代理使用说明

## 功能概述

WebSocketProxyHandler 提供了一个WebSocket代理功能，允许前端通过服务器作为中介与设备的WebSocket进行通信。

## 工作流程

1. **前端连接**: 前端连接到服务器的WebSocket端点 `/proxy-websocket`
2. **设备IP获取**: 前端发送包含 `deviceIp` 字段的JSON消息
3. **设备连接**: 服务器解析deviceIp并建立到设备WebSocket的连接
4. **消息转发**: 
   - 前端 → 服务器 → 设备
   - 设备 → 服务器 → 前端

## 使用方法

### 前端连接示例

```javascript
// 连接到代理WebSocket
const ws = new WebSocket('ws://localhost:8080/proxy-websocket');

ws.onopen = function() {
    console.log('连接到代理服务器成功');
    
    // 发送包含设备IP的消息
    const message = {
        deviceIp: '*************',  // 设备IP地址
        data: 'Hello Device'        // 要发送给设备的数据
    };
    
    ws.send(JSON.stringify(message));
};

ws.onmessage = function(event) {
    console.log('收到设备消息:', event.data);
    // 处理从设备返回的消息
};

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};

ws.onclose = function() {
    console.log('WebSocket连接已关闭');
};
```

### 消息格式

#### 前端发送给服务器的消息格式
```json
{
    "deviceIp": "*************:8080",  // 设备IP和端口（可选端口）
    "command": "getData",               // 命令类型
    "params": {                        // 参数
        "key": "value"
    }
}
```

#### 错误消息格式
```json
{
    "error": "连接设备失败: Connection refused"
}
```

## 配置说明

### 默认配置
- 默认设备端口: `8080`
- 默认设备WebSocket路径: `/websocket`
- 连接超时时间: `10秒`

### 设备WebSocket URL构建规则
- 如果deviceIp包含端口: `ws://*************:9090/websocket`
- 如果deviceIp不包含端口: `ws://*************:8080/websocket`

## 特性

1. **自动连接管理**: 自动建立和管理到设备的WebSocket连接
2. **错误处理**: 完善的错误处理和日志记录
3. **连接复用**: 同一前端会话复用设备连接
4. **自动清理**: 前端断开时自动清理设备连接
5. **异步连接**: 设备连接建立过程不阻塞前端消息处理
6. **动态切换**: 支持同一前端会话连接不同设备

## 日志说明

系统会记录以下关键事件的日志：
- 前端连接建立/断开
- 设备连接建立/断开
- 消息转发
- 错误信息

## 注意事项

1. 确保设备的WebSocket服务正常运行
2. 检查网络连通性和防火墙设置
3. 设备IP格式正确（支持IP:PORT格式）
4. 前端需要处理错误消息
5. 建议在生产环境中配置适当的跨域策略

## 故障排除

### 常见问题

1. **连接设备失败**
   - 检查设备IP是否正确
   - 确认设备WebSocket服务是否启动
   - 检查网络连通性

2. **消息转发失败**
   - 检查消息格式是否正确
   - 确认设备连接是否正常
   - 查看服务器日志

3. **前端收不到设备消息**
   - 确认前端WebSocket连接正常
   - 检查设备是否正确发送消息
   - 查看服务器转发日志
