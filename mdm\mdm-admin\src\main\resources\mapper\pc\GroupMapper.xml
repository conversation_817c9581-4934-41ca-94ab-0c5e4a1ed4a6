<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.pc.mapper.GroupMapper">

    <resultMap type="Group" id="GroupResult">
        <result property="id"    column="id"    />
        <result property="groupName"    column="group_name"    />
        <result property="sort"    column="sort"    />
        <result property="parentId"    column="parent_id"    />
        <result property="parentIds"    column="parent_ids"    />
        <result property="apkIds"    column="apk_ids"    />
        <result property="userName"    column="user_name"    />
        <result property="password"    column="password"    />
        <result property="description"    column="description"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectGroupVo">
        select id, group_name, sort, parent_id, parent_ids, apk_ids, user_name, password, description, update_time from tb_group
    </sql>

    <select id="selectGroupList" parameterType="Group" resultMap="GroupResult">
        <include refid="selectGroupVo"/>
        <where>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="parentIds != null  and parentIds != ''"> and parent_ids = #{parentIds}</if>
            <if test="apkIds != null  and apkIds != ''"> and apk_ids = #{apkIds}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
    </select>

    <select id="selectGroupById" parameterType="Long" resultMap="GroupResult">
        <include refid="selectGroupVo"/>
        where id = #{id}
    </select>

    <insert id="insertGroup" parameterType="Group">
        insert into tb_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="sort != null">sort,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="parentIds != null">parent_ids,</if>
            <if test="apkIds != null">apk_ids,</if>
            <if test="userName != null">user_name,</if>
            <if test="password != null">password,</if>
            <if test="description != null">description,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="parentIds != null">#{parentIds},</if>
            <if test="apkIds != null">#{apkIds},</if>
            <if test="userName != null">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="description != null">#{description},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateGroup" parameterType="Group">
        update tb_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="parentIds != null">parent_ids = #{parentIds},</if>
            <if test="apkIds != null">apk_ids = #{apkIds},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGroupById" parameterType="Long">
        delete from tb_group where id = #{id}
    </delete>

    <delete id="deleteGroupByIds" parameterType="String">
        delete from tb_group where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>