package com.sprt.pc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.pc.domain.Device;
import com.sprt.pc.dto.DeviceDTO;
import com.sprt.pc.vo.DeviceVO;

import java.util.List;

/**
 * 设备Service接口
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
public interface DeviceService extends IService<Device> {
    /**
     * 查询设备
     *
     * @param id 设备主键
     * @return 设备
     */
    public Device selectDeviceById(Long id);

    /**
     * 查询设备列表
     *
     * @param deviceDTO 设备
     * @return 设备集合
     */
    public List<DeviceVO> selectDeviceList(DeviceDTO deviceDTO);

    /**
     * 新增设备
     *
     * @param device 设备
     * @return 结果
     */
    public int insertDevice(Device device);

    /**
     * 修改设备
     *
     * @param device 设备
     * @return 结果
     */
    public int updateDevice(Device device);

    /**
     * 批量删除设备
     *
     * @param ids 需要删除的设备主键集合
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] ids);

    /**
     * 删除设备信息
     *
     * @param id 设备主键
     * @return 结果
     */
    public int deleteDeviceById(Long id);

    /**
     * 查询设备列表
     * @param packageName
     * @param versionName
     * @return
     */
    public List<DeviceVO> selectInstallDevice(String packageName, String versionName);

    /**
     * 根据设备id查询设备列表
     * @param deviceIds
     * @return
     */
    public List<DeviceVO> getDeviceByDeviceIds(List<String> deviceIds);
}