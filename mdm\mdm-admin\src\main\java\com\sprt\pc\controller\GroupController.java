package com.sprt.pc.controller;

import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.enums.BusinessType;
import com.sprt.pc.domain.Group;
import com.sprt.pc.dto.DeviceGroupDTO;
import com.sprt.pc.service.GroupService;
import com.sprt.pc.vo.GroupVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分组Controller
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@RestController
@RequestMapping("/pc/group")
public class GroupController extends BaseController
{
    @Autowired
    private GroupService groupService;

    /**
     * 查询分组列表
     */
    @GetMapping("/list")
    public List<GroupVO> list(Group group)
    {
        return groupService.selectGroupList(group);
    }

    /**
     * 获取分组详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(groupService.selectGroupById(id));
    }

    /**
     * 新增分组
     */
    @Log(title = "分组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Group group)
    {
        return toAjax(groupService.insertGroup(group));
    }

    /**
     * 修改分组
     */
    @Log(title = "分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Group group)
    {
        return toAjax(groupService.updateGroup(group));
    }

    /**
     * 删除分组
     */
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(groupService.deleteGroupByIds(ids));
    }

    @PostMapping("updateGroup")
    public AjaxResult updateGroup(@RequestBody DeviceGroupDTO deviceGroupDTO)
    {
        return toAjax(groupService.updateDeviceGroup(deviceGroupDTO));
    }
}
