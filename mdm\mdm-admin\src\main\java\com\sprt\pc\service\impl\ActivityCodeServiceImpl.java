package com.sprt.pc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sprt.android.utils.DeviceIdUtil;
import com.sprt.code_analysis.utils.DecryptUtil;
import com.sprt.common.utils.DateUtils;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.ActivityCode;
import com.sprt.pc.domain.Device;
import com.sprt.pc.mapper.ActivityCodeMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.service.ActivityCodeService;
import com.sprt.pc.vo.ExpireActivityCodeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 注册码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Slf4j
@Service
public class ActivityCodeServiceImpl extends ServiceImpl<ActivityCodeMapper, ActivityCode> implements ActivityCodeService {
    @Autowired
    private ActivityCodeMapper activityCodeMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    /**
     * 查询注册码
     *
     * @param id 注册码主键
     * @return 注册码
     */
    @Override
    public ActivityCode selectActivityCodeById(Long id) {
        return activityCodeMapper.selectActivityCodeById(id);
    }

    /**
     * 查询注册码列表
     *
     * @param activityCode 注册码
     * @return 注册码
     */
    @Override
    @Transactional
    public List<ActivityCode> selectActivityCodeList(ActivityCode activityCode) {
        List<ActivityCode> activityCodes = activityCodeMapper.selectActivityCodeList(activityCode);
        int sumDevice = 0;
        try {
            for (ActivityCode code : activityCodes) {
                Map<String, String> stringObjectMap = DecryptUtil.decryptDaysAndDeviceId(code.getCode());
                String days = stringObjectMap.get("days");
                String deviceId = stringObjectMap.get("deviceId");
                String deviceSum = stringObjectMap.get("deviceNum");
                if (DeviceIdUtil.generateDeviceId().equals(deviceId)) {
                    if (StringUtils.isEmpty(code.getExpireTime()) || StringUtils.isEmpty(code.getDeviceSum())) {
                        throw new RuntimeException("激活码信息不完整");
                    }
                    sumDevice += Integer.parseInt(deviceSum);
                    if (!days.equals(code.getExpireTime()) || !code.getDeviceSum().equals(deviceSum)) {
                        // 信息不匹配 更新字段数据
                        ActivityCode activityCodeUpdate = new ActivityCode();
                        BeanUtils.copyProperties(code, activityCodeUpdate);
                        activityCodeUpdate.setExpireTime(days);
                        activityCodeUpdate.setDeviceSum(deviceSum);
                        if (Long.parseLong(days) < System.currentTimeMillis()) {
                            activityCodeUpdate.setType(1);
                        }
                        activityCodeMapper.updateActivityCode(activityCodeUpdate);
                        activityCodes = activityCodeMapper.selectActivityCodeList(activityCode);
                    }
                } else {
                    throw new RuntimeException("激活码无效");
                }
            }
            // 更新设备激活状态
            List<Device> devices = deviceMapper.selectList(null);
            for (Device device : devices) {
                if (sumDevice > 0) {
                    if (device.getStatus() == 2) {
                        device.setStatus(1);
                        sumDevice--;
                        deviceMapper.updateDevice(device);
                    }
                } else {
                    device.setStatus(2);
                    deviceMapper.updateDevice(device);
                }
            }
        } catch (Exception e) {
            log.error("查询注册码失败");
            throw new RuntimeException("查询注册码失败", e);
        }
        return activityCodes;
    }

    /**
     * 新增注册码
     *
     * @param activityCode 注册码
     * @return 结果
     */
    @Override
    public int insertActivityCode(ActivityCode activityCode) {
        // 解析激活码
        try {
            Map<String, String> stringObjectMap = DecryptUtil.decryptDaysAndDeviceId(activityCode.getCode());
            String days = stringObjectMap.get("days");
            String deviceId = stringObjectMap.get("deviceId");
            String deviceSum = stringObjectMap.get("deviceNum");
            if (DeviceIdUtil.generateDeviceId().equals(deviceId)) {
                activityCode.setExpireTime(days);
                activityCode.setCode(activityCode.getCode());
                activityCode.setDeviceSum(deviceSum);
            } else {
                throw new RuntimeException("激活码无效");
            }
        } catch (Exception e) {
            log.error("添加激活码失败：", e);
            throw new RuntimeException("激活码无效", e);
        }
        activityCode.setCreateTime(DateUtils.getNowDate());
        return activityCodeMapper.insert(activityCode);
    }

    /**
     * 修改注册码
     *
     * @param activityCode 注册码
     * @return 结果
     */
    @Override
    public int updateActivityCode(ActivityCode activityCode) {
        return activityCodeMapper.updateActivityCode(activityCode);
    }

    /**
     * 批量删除注册码
     *
     * @param ids 需要删除的注册码主键
     * @return 结果
     */
    @Override
    public int deleteActivityCodeByIds(Long[] ids) {
        return activityCodeMapper.deleteActivityCodeByIds(ids);
    }

    /**
     * 删除注册码信息
     *
     * @param id 注册码主键
     * @return 结果
     */
    @Override
    public int deleteActivityCodeById(Long id) {
        return activityCodeMapper.deleteActivityCodeById(id);
    }

    @Override
    public List<ExpireActivityCodeVO> getExpireActivityCode() throws Exception {
        List<ActivityCode> activityCodeList = activityCodeMapper.selectList(null);
        int allDeviceCount = 0;
        for (ActivityCode activityCode : activityCodeList) {
            Map<String, String> stringObjectMap = DecryptUtil.decryptDaysAndDeviceId(activityCode.getCode());
            String days = stringObjectMap.get("days");
            String deviceId = stringObjectMap.get("deviceId");
            String deviceSum = stringObjectMap.get("deviceNum");
            allDeviceCount+=Integer.parseInt(deviceSum);
            if (DeviceIdUtil.generateDeviceId().equals(deviceId)) {
                if (StringUtils.isEmpty(activityCode.getExpireTime()) || StringUtils.isEmpty(activityCode.getDeviceSum())) {
                    throw new RuntimeException("激活码信息不完整");
                }
                if (!days.equals(activityCode.getExpireTime()) || !activityCode.getDeviceSum().equals(deviceSum)) {
                    // 信息不匹配 更新字段数据
                    ActivityCode activityCodeUpdate = new ActivityCode();
                    BeanUtils.copyProperties(activityCode, activityCodeUpdate);
                    activityCodeUpdate.setExpireTime(days);
                    activityCodeUpdate.setDeviceSum(deviceSum);
                    if (Long.parseLong(days) < System.currentTimeMillis()) {
                        activityCodeUpdate.setType(1);
                    }
                    activityCodeMapper.updateActivityCode(activityCodeUpdate);
                }
            } else {
                throw new RuntimeException("激活码无效");
            }
        }

        int expireCount = 0;
        activityCodeList = activityCodeMapper.selectList(null);
        long nowTime = System.currentTimeMillis();
        if(activityCodeList.isEmpty()){
            throw new RuntimeException("没有激活码可用");
        }

        List<ExpireActivityCodeVO> expireActivityCodeVOList = new ArrayList<>();
        for (ActivityCode activityCode1 : activityCodeList) {
            long gapTime = Long.parseLong(activityCode1.getExpireTime()) - nowTime;
            long monthTime = 1000L * 60L * 60L * 24L * 30L;
            if (gapTime > 0 && gapTime < monthTime) {
                long day = (int)Math.ceil(gapTime / (1000 * 60 * 60 * 24.0));
                ExpireActivityCodeVO expireActivityCodeVO = new ExpireActivityCodeVO();
                expireActivityCodeVO.setDeviceCount(Integer.parseInt(activityCode1.getDeviceSum()));
                expireActivityCodeVO.setDay((int)day);
                expireActivityCodeVOList.add(expireActivityCodeVO);
            } else if (gapTime < 0) {
                expireCount++;
            }
        }

        if (activityCodeList.size() > 0 && activityCodeList.size() == expireCount) {
            // 全部过期
            expireActivityCodeVOList.add(new ExpireActivityCodeVO(allDeviceCount, -1));
            return expireActivityCodeVOList;
        }
        if (expireActivityCodeVOList.size() == 0 && activityCodeList.size() != expireCount) {
            // 没有即将过期的激活码并且不是全都过期
            expireActivityCodeVOList.add(new ExpireActivityCodeVO(0, -2));
            return expireActivityCodeVOList;
        }
        return expireActivityCodeVOList;
    }

    public static void main(String[] args) {
        long nowTime = System.currentTimeMillis();
        long gapTime = Long.parseLong("1752894064721") - nowTime;
        System.out.println(gapTime);
        System.out.println();
    }

}