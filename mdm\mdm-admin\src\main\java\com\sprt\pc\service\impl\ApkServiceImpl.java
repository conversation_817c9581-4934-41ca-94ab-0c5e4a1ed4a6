package com.sprt.pc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sprt.common.utils.DateUtils;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.GroupApk;
import com.sprt.pc.dto.ApkDTO;
import com.sprt.pc.mapper.ApkMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupApkMapper;
import com.sprt.pc.service.ApkService;
import com.sprt.pc.service.DeviceService;
import com.sprt.pc.vo.DeviceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * apk信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Slf4j
@Service
public class ApkServiceImpl extends ServiceImpl<ApkMapper, Apk> implements ApkService {
    @Autowired
    private ApkMapper apkMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private GroupApkMapper groupApkMapper;

    /**
     * 查询apk信息
     *
     * @param id apk信息主键
     * @return apk信息
     */
    @Override
    public Apk selectApkById(Long id) {
        return apkMapper.selectApkById(id);
    }

    /**
     * 查询apk信息列表
     *
     * @param apk apk信息
     * @return apk信息
     */
    @Override
    public List<Apk> selectApkList(Apk apk) {
        return apkMapper.selectApkList(apk);
    }

    /**
     * 新增apk信息
     *
     * @param apk apk信息
     * @return 结果
     */
    @Override
    public int insertApk(Apk apk) {
        apk.setCreateTime(DateUtils.getNowDate());
        return apkMapper.insert(apk);
    }

    /**
     * 修改apk信息
     *
     * @param apk apk信息
     * @return 结果
     */
    @Override
    public int updateApk(Apk apk) {
        return apkMapper.updateApk(apk);
    }

    /**
     * 批量删除apk信息
     *
     * @param ids 需要删除的apk信息主键
     * @return 结果
     */
    @Override
    public int deleteApkByIds(Long[] ids) {
        List<GroupApk> groupApks = groupApkMapper.selectList(new LambdaQueryWrapper<GroupApk>().in(GroupApk::getApkId, Arrays.asList(ids)));
        if (groupApks.isEmpty()) {
            int count = apkMapper.deleteApkByIds(ids);
            return count > 0 ? 2 : 0;
        }
        return 1;
    }

    /**
     * 删除apk信息信息
     *
     * @param id apk信息主键
     * @return 结果
     */
    @Override
    public int deleteApkById(Long id) {
        return apkMapper.deleteApkById(id);
    }

    @Override
    public Apk selectApkByFilePath(String filePath) {
        return apkMapper.selectApkByFilePath(filePath);
    }

    @Override
    public List<Apk> selectApkListByPath(ApkDTO apkDTO) {
        if(StringUtils.isEmpty(apkDTO.getPath())){
            apkDTO.setPath("\\filePath");
        }
        String projectPath = System.getProperty("user.dir") + apkDTO.getPath();
        log.info("projectPath: {}", projectPath);
        apkDTO.setPath(projectPath);
        List<Apk> apks = apkMapper.selectApkListByPath(apkDTO);
        String apkDataBasePath = projectPath;
        if(apks.isEmpty()){
            apkDataBasePath = projectPath.replace("\\","\\\\");
            apkDTO.setPath(apkDataBasePath);
            apks = apkMapper.selectApkListByPath(apkDTO);
        }

        if(apks.isEmpty()){
            apkDataBasePath = projectPath.replace("\\", "/");
            apkDTO.setPath(apkDataBasePath);
            apks = apkMapper.selectApkListByPath(apkDTO);
        }
        for (Apk apk : apks) {
            String property = System.getProperty("user.dir").replace("\\","/");
            String apkPath = apk.getFilePath().replace( property + "/", "");
            apk.setFilePath(apkPath);
        }
        return apks;
    }

    @Override
    public void deleteDirectoryByPath(String path) {
        String databasePath = path.replace("\\", "/") + "/";
        apkMapper.delete(new LambdaQueryWrapper<Apk>().like(Apk::getFilePath, databasePath));
    }

    @Override
    public void deleteDirectoryFile(String path) {
//        String databasePath = path.replace("\\", "\\\\");
        apkMapper.delete(new LambdaQueryWrapper<Apk>().eq(Apk::getFilePath, path));
    }

    @Override
    public List<DeviceVO> getInstallDevice(Map<String, String> params) {
        String packageName = params.get("packageName");
        String versionName = params.get("versionName");

        return deviceService.selectInstallDevice(packageName, versionName);
    }

    @Override
    public List<Apk> getApkByApkId(List<String> apkIds) {
        return apkMapper.selectApkByApkId(apkIds);
    }
}