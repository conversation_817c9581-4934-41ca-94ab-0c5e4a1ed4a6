package com.sprt.pc.websocket;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
public class WebSocketProxyHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketProxyHandler.class);
    private static final int CONNECTION_TIMEOUT = 10; // 连接超时时间（秒）
    private static final String DEFAULT_DEVICE_PORT = "8080";
    private static final String DEFAULT_DEVICE_PATH = "/websocket";

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final WebSocketClient webSocketClient = new StandardWebSocketClient();

    // 存储前端会话
    private final ConcurrentHashMap<String, WebSocketSession> frontendSessions = new ConcurrentHashMap<>();
    // 存储设备会话，key为前端sessionId
    private final ConcurrentHashMap<String, WebSocketSession> deviceSessions = new ConcurrentHashMap<>();
    // 存储设备IP映射，key为前端sessionId
    private final ConcurrentHashMap<String, String> deviceIpMapping = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        frontendSessions.put(sessionId, session);
        logger.info("前端WebSocket连接已建立，SessionId: {}, 远程地址: {}",
                   sessionId, session.getRemoteAddress());

        // 发送连接成功消息
        try {
            String welcomeMessage = "{\"type\":\"connection\",\"status\":\"connected\",\"message\":\"WebSocket代理连接成功\"}";
            session.sendMessage(new TextMessage(welcomeMessage));
        } catch (Exception e) {
            logger.error("发送欢迎消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleMessage(WebSocketSession frontendSession, WebSocketMessage<?> message) throws Exception {
        String sessionId = frontendSession.getId();
        String payload = message.getPayload().toString();

        try {
            // 解析消息获取deviceIp
            String deviceIp = extractDeviceIp(payload, sessionId);

            if (deviceIp != null && !deviceIp.isEmpty()) {
                // 如果是新的设备IP或设备连接不存在，建立新连接
                String currentDeviceIp = deviceIpMapping.get(sessionId);
                if (!deviceIp.equals(currentDeviceIp) || !deviceSessions.containsKey(sessionId)
                    || !deviceSessions.get(sessionId).isOpen()) {

                    // 关闭旧的设备连接
                    closeDeviceConnection(sessionId);

                    // 建立新的设备连接
                    connectToDevice(deviceIp, sessionId);
                }
            }

            // 转发消息给设备
            forwardMessageToDevice(sessionId, payload);

        } catch (Exception e) {
            logger.error("处理前端消息时发生错误，SessionId: {}, 错误: {}", sessionId, e.getMessage(), e);
            sendErrorToFrontend(frontendSession, "消息处理失败: " + e.getMessage());
        }
    }

    /**
     * 从消息中提取设备IP地址
     */
    private String extractDeviceIp(String payload, String sessionId) {
        try {
            JsonNode jsonNode = objectMapper.readTree(payload);
            if (jsonNode.has("deviceIp")) {
                return jsonNode.get("deviceIp").asText();
            }
        } catch (Exception e) {
            logger.warn("解析消息中的deviceIp失败，SessionId: {}, 消息: {}, 错误: {}",
                       sessionId, payload, e.getMessage());
        }
        return null;
    }

    /**
     * 转发消息给设备
     */
    private void forwardMessageToDevice(String sessionId, String payload) {
        WebSocketSession deviceSession = deviceSessions.get(sessionId);
        if (deviceSession != null && deviceSession.isOpen()) {
            try {
                deviceSession.sendMessage(new TextMessage(payload));
                logger.debug("消息已转发给设备，SessionId: {}", sessionId);
            } catch (Exception e) {
                logger.error("转发消息给设备失败，SessionId: {}, 错误: {}", sessionId, e.getMessage(), e);
            }
        } else {
            logger.warn("设备连接不可用，无法转发消息，SessionId: {}", sessionId);
        }
    }

    /**
     * 向前端发送错误消息
     */
    private void sendErrorToFrontend(WebSocketSession frontendSession, String errorMessage) {
        try {
            if (frontendSession.isOpen()) {
                String errorJson = String.format("{\"error\": \"%s\"}", errorMessage);
                frontendSession.sendMessage(new TextMessage(errorJson));
            }
        } catch (Exception e) {
            logger.error("发送错误消息给前端失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 连接到设备的WebSocket
     */
    private void connectToDevice(String deviceIp, String sessionId) {
        CompletableFuture.runAsync(() -> {
            try {
                // 构建设备WebSocket URI
                String deviceUrl = buildDeviceWebSocketUrl(deviceIp);
                URI deviceUri = new URI(deviceUrl);

                logger.info("正在连接设备WebSocket，设备IP: {}, SessionId: {}", deviceIp, sessionId);

                // 创建设备WebSocket处理器
                WebSocketHandler deviceHandler = new DeviceWebSocketHandler(sessionId, deviceIp);

                // 建立连接
                WebSocketSession deviceSession = webSocketClient.doHandshake(
                    deviceHandler, null, deviceUri
                ).get(CONNECTION_TIMEOUT, TimeUnit.SECONDS);

                // 保存连接信息
                deviceSessions.put(sessionId, deviceSession);
                deviceIpMapping.put(sessionId, deviceIp);

                logger.info("设备WebSocket连接成功，设备IP: {}, SessionId: {}", deviceIp, sessionId);

            } catch (Exception e) {
                logger.error("连接设备WebSocket失败，设备IP: {}, SessionId: {}, 错误: {}",
                           deviceIp, sessionId, e.getMessage(), e);

                // 通知前端连接失败
                WebSocketSession frontendSession = frontendSessions.get(sessionId);
                if (frontendSession != null) {
                    sendErrorToFrontend(frontendSession, "连接设备失败: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 构建设备WebSocket URL
     */
    private String buildDeviceWebSocketUrl(String deviceIp) {
        // 如果deviceIp已包含端口，直接使用；否则添加默认端口
        if (deviceIp.contains(":")) {
            return "ws://" + deviceIp + DEFAULT_DEVICE_PATH;
        } else {
            return "ws://" + deviceIp + ":" + DEFAULT_DEVICE_PORT + DEFAULT_DEVICE_PATH;
        }
    }

    /**
     * 关闭设备连接
     */
    private void closeDeviceConnection(String sessionId) {
        WebSocketSession deviceSession = deviceSessions.remove(sessionId);
        if (deviceSession != null && deviceSession.isOpen()) {
            try {
                deviceSession.close();
                logger.info("设备连接已关闭，SessionId: {}", sessionId);
            } catch (Exception e) {
                logger.error("关闭设备连接时发生错误，SessionId: {}, 错误: {}", sessionId, e.getMessage(), e);
            }
        }
        deviceIpMapping.remove(sessionId);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        logger.error("前端WebSocket传输错误，SessionId: {}, 错误: {}", sessionId, exception.getMessage(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        logger.info("前端WebSocket连接已关闭，SessionId: {}, 关闭状态: {}", sessionId, closeStatus);

        // 移除前端会话
        frontendSessions.remove(sessionId);

        // 关闭对应的设备连接
        closeDeviceConnection(sessionId);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 设备WebSocket处理器内部类
     */
    private class DeviceWebSocketHandler implements WebSocketHandler {
        private final String frontendSessionId;
        private final String deviceIp;

        public DeviceWebSocketHandler(String frontendSessionId, String deviceIp) {
            this.frontendSessionId = frontendSessionId;
            this.deviceIp = deviceIp;
        }

        @Override
        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
            logger.info("设备WebSocket连接已建立，设备IP: {}, 前端SessionId: {}", deviceIp, frontendSessionId);
        }

        @Override
        public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
            // 转发设备消息给前端
            WebSocketSession frontendSession = frontendSessions.get(frontendSessionId);
            if (frontendSession != null && frontendSession.isOpen()) {
                try {
                    frontendSession.sendMessage(message);
                    logger.debug("设备消息已转发给前端，前端SessionId: {}", frontendSessionId);
                } catch (Exception e) {
                    logger.error("转发设备消息给前端失败，前端SessionId: {}, 错误: {}",
                               frontendSessionId, e.getMessage(), e);
                }
            } else {
                logger.warn("前端连接不可用，无法转发设备消息，前端SessionId: {}", frontendSessionId);
                // 前端连接已断开，关闭设备连接
                if (session.isOpen()) {
                    session.close();
                }
            }
        }

        @Override
        public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
            logger.error("设备WebSocket传输错误，设备IP: {}, 前端SessionId: {}, 错误: {}",
                        deviceIp, frontendSessionId, exception.getMessage(), exception);

            // 通知前端设备连接出错
            WebSocketSession frontendSession = frontendSessions.get(frontendSessionId);
            if (frontendSession != null) {
                sendErrorToFrontend(frontendSession, "设备连接错误: " + exception.getMessage());
            }
        }

        @Override
        public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
            logger.info("设备WebSocket连接已关闭，设备IP: {}, 前端SessionId: {}, 关闭状态: {}",
                       deviceIp, frontendSessionId, closeStatus);

            // 从映射中移除
            deviceSessions.remove(frontendSessionId);
            deviceIpMapping.remove(frontendSessionId);

            // 通知前端设备连接已断开
            WebSocketSession frontendSession = frontendSessions.get(frontendSessionId);
            if (frontendSession != null && frontendSession.isOpen()) {
                sendErrorToFrontend(frontendSession, "设备连接已断开");
            }
        }

        @Override
        public boolean supportsPartialMessages() {
            return false;
        }
    }
}