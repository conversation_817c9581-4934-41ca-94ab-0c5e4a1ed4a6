package com.sprt.pc.websocket;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class WebSocketProxyHandler implements WebSocketHandler {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final WebSocketClient webSocketClient = new StandardWebSocketClient();
    private final ConcurrentHashMap<String, WebSocketSession> frontendSessions = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, WebSocketSession> deviceSessions = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        System.out.println("Frontend connected: " + session.getId());
    }
    
    @Override
    public void handleMessage(WebSocketSession frontendSession, WebSocketMessage<?> message) throws Exception {
        String payload = message.getPayload().toString();
        JsonNode jsonNode = objectMapper.readTree(payload);
        
        // 获取deviceIp
        String deviceIp = jsonNode.get("deviceIp").asText();
        String sessionId = frontendSession.getId();
        
        frontendSessions.put(sessionId, frontendSession);
        
        // 如果设备连接不存在，创建新连接
        if (!deviceSessions.containsKey(sessionId)) {
            connectToDevice(deviceIp, sessionId);
        }
        
        // 转发消息给设备
        WebSocketSession deviceSession = deviceSessions.get(sessionId);
        if (deviceSession != null && deviceSession.isOpen()) {
            deviceSession.sendMessage(new TextMessage(payload));
        }
    }
    
    private void connectToDevice(String deviceIp, String sessionId) {
        try {
            URI deviceUri = new URI("ws://" + deviceIp + ":8080/websocket");
            
            WebSocketSession deviceSession = webSocketClient.doHandshake(new WebSocketHandler() {
                @Override
                public void afterConnectionEstablished(WebSocketSession session) {
                    deviceSessions.put(sessionId, session);
                    System.out.println("Connected to device: " + deviceIp);
                }
                
                @Override
                public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                    // 转发设备消息给前端
                    WebSocketSession frontendSession = frontendSessions.get(sessionId);
                    if (frontendSession != null && frontendSession.isOpen()) {
                        frontendSession.sendMessage(message);
                    }
                }
                
                @Override
                public void handleTransportError(WebSocketSession session, Throwable exception) {
                    System.err.println("Device connection error: " + exception.getMessage());
                }
                
                @Override
                public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
                    deviceSessions.remove(sessionId);
                    System.out.println("Device disconnected: " + deviceIp);
                }
                
                @Override
                public boolean supportsPartialMessages() {
                    return false;
                }
            }, null, deviceUri).get();
            
        } catch (Exception e) {
            System.err.println("Failed to connect to device: " + e.getMessage());
        }
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.err.println("Frontend connection error: " + exception.getMessage());
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        frontendSessions.remove(sessionId);
        
        // 关闭对应的设备连接
        WebSocketSession deviceSession = deviceSessions.remove(sessionId);
        if (deviceSession != null && deviceSession.isOpen()) {
            deviceSession.close();
        }
        
        System.out.println("Frontend disconnected: " + sessionId);
    }
    
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}