package com.sprt.android.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sprt.android.domain.AndroidLoginBody;
import com.sprt.android.service.LoginService;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Device;
import com.sprt.pc.domain.Group;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class LoginServiceImpl implements LoginService {

    private static final String salt = ">>sE1o%@/;y^K{'KJIpCZ>y88";
    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public int login(AndroidLoginBody androidLoginBody) {
        if (StringUtils.isEmpty(androidLoginBody.getUsername())) {
            throw new RuntimeException("用户名不能为空！");
        }
        if (StringUtils.isEmpty(androidLoginBody.getPassword())) {
            throw new RuntimeException("密码不能为空！");
        }
        List<Group> groups = groupMapper.selectList(new LambdaQueryWrapper<Group>().eq(Group::getUserName, androidLoginBody.getUsername()));
        if (groups.isEmpty()) {
            throw new RuntimeException("用户名不存在！");
        }

        if (!groups.get(0).getPassword().equals(androidLoginBody.getPassword())) {
            throw new RuntimeException("密码错误！");
        } else {
            // 登录成功 修改设备分组
            Device device = new Device();
            BeanUtils.copyProperties(androidLoginBody, device);
            device.setStatus(1);
            device.setGroupId(groups.get(0).getId());
            Long count = 0L;
            if (StringUtils.isNotEmpty(device.getMac())) {
                count = deviceMapper.selectCount(new LambdaQueryWrapper<Device>().eq(Device::getMac, device.getMac()));
            } else if (StringUtils.isNotEmpty(device.getSn())) {
                count = deviceMapper.selectCount(new LambdaQueryWrapper<Device>().eq(Device::getSn, device.getSn()));
            } else if (StringUtils.isNotEmpty(device.getDeviceId())) {
                count = deviceMapper.selectCount(new LambdaQueryWrapper<Device>().eq(Device::getDeviceId, device.getDeviceId()));
            }

            if (count == 0) {
                device.setCreateTime(new Date());
                return deviceMapper.insert(device);
            } else {
                return deviceMapper.updateDeviceBySn(device.getMac(), device.getIp(), device.getGroupId(), device.getAssetCode(), device.getSn(), device.getDeviceId(), device.getPlatform(), device.getVersion(), device.getBattery(), device.getRssi(), device.getWifiName(), 1, new Date(), device.getActivityTime(), device.getRemainingStorage(), device.getApMac(), device.getIsRecharge(),device.getDeviceName());
            }
        }
    }

}
