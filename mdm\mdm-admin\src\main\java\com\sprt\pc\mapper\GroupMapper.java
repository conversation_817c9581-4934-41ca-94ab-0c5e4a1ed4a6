package com.sprt.pc.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.pc.domain.Group;
import org.springframework.stereotype.Repository;

/**
 * 分组Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Repository
public interface GroupMapper extends BaseMapper<Group> {
    /**
     * 查询分组
     *
     * @param id 分组主键
     * @return 分组
     */
    public Group selectGroupById(Long id);

    /**
     * 查询分组列表
     *
     * @param group 分组
     * @return 分组集合
     */
    public List<Group> selectGroupList(Group group);

    /**
     * 新增分组
     *
     * @param group 分组
     * @return 结果
     */
    public int insertGroup(Group group);

    /**
     * 修改分组
     *
     * @param group 分组
     * @return 结果
     */
    public int updateGroup(Group group);

    /**
     * 删除分组
     *
     * @param id 分组主键
     * @return 结果
     */
    public int deleteGroupById(Long id);

    /**
     * 批量删除分组
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGroupByIds(Long[] ids);
}