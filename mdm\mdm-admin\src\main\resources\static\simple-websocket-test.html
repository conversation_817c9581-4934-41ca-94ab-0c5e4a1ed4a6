<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简单WebSocket测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; }
        button { padding: 10px; margin: 5px; }
        .log { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: auto; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试</h1>
        
        <div>
            <label>WebSocket地址:</label>
            <input type="text" id="wsUrl" value="ws://localhost:5006/proxy-websocket" style="width: 300px;">
        </div>
        
        <div style="margin: 10px 0;">
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开</button>
            <button onclick="sendTest()">发送测试消息</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div>
            <label>设备IP:</label>
            <input type="text" id="deviceIp" value="*************" style="width: 150px;">
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            if (ws) {
                log('关闭现有连接...', 'info');
                ws.close();
            }
            
            log(`尝试连接到: ${url}`, 'info');
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function() {
                    log('✅ WebSocket连接成功!', 'success');
                };
                
                ws.onmessage = function(event) {
                    log(`📨 收到消息: ${event.data}`, 'success');
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket错误: ${error}`, 'error');
                };
                
                ws.onclose = function(event) {
                    log(`🔌 连接关闭 - 代码: ${event.code}, 原因: ${event.reason}`, 'info');
                };
                
            } catch (error) {
                log(`❌ 连接失败: ${error.message}`, 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                log('手动断开连接', 'info');
            }
        }
        
        function sendTest() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket未连接', 'error');
                return;
            }
            
            const deviceIp = document.getElementById('deviceIp').value;
            const message = {
                deviceIp: deviceIp,
                command: 'test',
                timestamp: new Date().toISOString(),
                data: 'Hello from test page'
            };
            
            const jsonMessage = JSON.stringify(message);
            ws.send(jsonMessage);
            log(`📤 发送消息: ${jsonMessage}`, 'info');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载时自动检查服务状态
        window.onload = function() {
            fetch('/api/websocket/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        log('✅ WebSocket服务状态正常', 'success');
                    } else {
                        log('❌ WebSocket服务异常: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    log('❌ 无法检查服务状态: ' + error.message, 'error');
                });
        };
    </script>
</body>
</html>
