package com.sprt.pc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupDTO {
    /** 主键 */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /** 组名 */
    @Excel(name = "组名" )
    private String groupName;

    /** 排序 */
    @Excel(name = "排序" )
    private Long sort;

    /** 父级id */
    @Excel(name = "父级id" )
    private Long parentId;

    /** 所有祖籍 */
    @Excel(name = "所有祖籍" )
    private String parentIds;

    /** 配置apk列表 */
    @Excel(name = "配置apk列表" )
    private String apkIds;

    /** 账号 */
    @Excel(name = "账号" )
    private String userName;

    /** 密码 */
    @Excel(name = "密码" )
    private String password;

    /** 描述 */
    @Excel(name = "描述" )
    private String description;

    private String where;
}
