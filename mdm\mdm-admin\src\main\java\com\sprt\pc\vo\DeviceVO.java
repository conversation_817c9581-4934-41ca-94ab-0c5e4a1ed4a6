package com.sprt.pc.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import com.sprt.pc.domain.Apk;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceVO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * mac地址
     */
    @Excel(name = "mac地址")
    private String mac;

    /**
     * 设备sn
     */
    @Excel(name = "设备sn")
    private String sn;

    /**
     * ip地址
     */
    @Excel(name = "ip地址")
    private String ip;

    /**
     * 设备型号
     */
    @Excel(name = "设备型号")
    private String model;

    /**
     * 平台
     */
    @Excel(name = "平台")
    private String platform;

    /**
     * 版本
     */
    @Excel(name = "版本")
    private String version;

    /**
     * 在线状态，0:离线，1:在线，2:未激活
     */
    @Excel(name = "在线状态，0:离线，1:在线，2:未激活")
    private Integer status;

    /** wifi信号强度 */
    @Excel(name = "wifi信号强度" )
    private String rssi;

    /** wifi名称 */
    @Excel(name = "wifi名称" )
    private String wifiName;

    /** 电池电量 */
    @Excel(name = "电池电量" )
    private String battery;

    /** 位置 */
    @Excel(name = "位置" )
    private String location;

    /** 活跃时间 */
    @Excel(name = "活跃时间" )
    private String activityTime;

    /** apMac */
    @Excel(name = "apMac" )
    private String apMac;

    /** 充电状态 */
    @Excel(name = "充电状态" )
    private String isRecharge;

    /** 剩余存储 */
    @Excel(name = "剩余存储" )
    private String remainingStorage;

    /**
     * 分组id
     */
    private String groupId;

    @Excel(name = "分组名称")
    private String groupName;

    @Excel(name = "资产资产编号")
    private String assetCode;

    @Excel(name = "设备名称")
    private String deviceName;

    private String deviceId;

    @Excel(name = "备注")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date heartbeatTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private List<Apk> apkList;
}
