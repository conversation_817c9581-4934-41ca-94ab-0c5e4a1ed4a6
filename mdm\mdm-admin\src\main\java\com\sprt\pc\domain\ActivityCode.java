package com.sprt.pc.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 注册码对象 tb_activity_code
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@TableName("tb_activity_code")
public class ActivityCode {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 激活码
     */
    @Excel(name = "激活码")
    private String code;

    /**
     * 激活时长
     */
    @Excel(name = "激活时长")
    private String expireTime;

    /**
     * 状态：0正常，1过期
     */
    @Excel(name = "状态：0正常，1过期")
    private Integer type;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String deviceSum;

    public ActivityCode(Long id, String code, String expireTime, Integer type, Date createTime, String deviceSum) {
        this.id = id;
        this.code = code;
        this.expireTime = expireTime;
        this.type = type;
        this.createTime = createTime;
        this.deviceSum = deviceSum;
    }

    public ActivityCode() {
    }

}
