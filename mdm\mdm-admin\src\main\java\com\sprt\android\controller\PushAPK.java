package com.sprt.android.controller;

import com.alibaba.fastjson2.JSON;
import com.sprt.android.domain.HeartbeatBody;
import com.sprt.android.domain.rfid.DisplayMessage;
import com.sprt.android.domain.rfid.ReaderFrom;
import com.sprt.android.domain.rfid.ReaderResponse;
import com.sprt.android.dto.DownPathDTO;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.utils.StringUtils;
import com.sprt.common.utils.file.FileUtils;
import com.sprt.common.utils.ip.IpUtils;
import com.sprt.pc.service.ApkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import static com.sprt.common.core.domain.AjaxResult.error;
import static com.sprt.common.core.domain.AjaxResult.success;

@Slf4j
@RestController
@RequestMapping("android/push")
public class PushAPK {

    @Autowired
    private ApkService apkService;

    private static final String IP = "************";
    private static final String PORT = "9901";

    @PostMapping("pushHeartbeat")
    public void pushHeartbeat(@RequestBody HeartbeatBody heartbeatBody) {
        System.out.println("接收到心跳包：时间" + new Date() + ":" + heartbeatBody.toString());
    }

    @PostMapping("/apk")
    public AjaxResult apk(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return error("文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return error("文件名解析失败");
        }

        try {
            // 获取当前日期
            LocalDate currentDate = LocalDate.now();
            int year = currentDate.getYear();
            int month = currentDate.getMonthValue();

            // 构建文件夹路径
            String projectPath = System.getProperty("user.dir");
            Path directoryPath = Paths.get(projectPath, "apk", year + "", String.format("%02d", month));

            // 创建目录（如果不存在）
            Files.createDirectories(directoryPath);

            // 目标文件路径
            Path destinationPath = directoryPath.resolve(originalFilename);

            // 直接存储文件，避免使用 InputStream
            file.transferTo(destinationPath.toFile());
            log.info("成功保存 APK 文件: {}", originalFilename);

            return success("文件上传成功");
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return error("文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/apkDownload")
    public ResponseEntity<StreamingResponseBody> apkDownload(HttpServletRequest request, @RequestBody DownPathDTO pakPath) {
        try {
            String path = pakPath.getPath();
            if (!FileUtils.checkAllowDownload(path)) {
                throw new IllegalArgumentException("非法路径");
            }

            Path filePath = Paths.get(System.getProperty("user.dir"), "filePath", path).normalize();
            File file = filePath.toFile();

            if (!file.exists() || !file.isFile()) {
                log.info(IpUtils.getIpAddr(request)+"下载文件不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            }

            StreamingResponseBody body = outputStream -> {
                try (InputStream inputStream = new FileInputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();
                }
            };

            String encodedFileName = encodeRfc5987(file.getName());
            log.info(IpUtils.getIpAddr(request)+"下载文件："+path);
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                    .body(body);

        } catch (Exception e) {
            log.error("下载异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    // RFC 5987编码方法
    private String encodeRfc5987(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString()).replace("+", "%20");
        } catch (Exception e) {
            throw new RuntimeException("Failed to encode filename", e);
        }
    }

    @PostMapping("CheckResourceCarde")
    public String CheckResourceCarde(@RequestBody ReaderFrom readerFrom) {
        System.out.println(readerFrom.toString());

        try {
            DisplayMessage displayMessage1 = new DisplayMessage(true, 1, "机台[XXXXXX]无技能需求。");
            DisplayMessage displayMessage2 = new DisplayMessage(true, 1, "员工[YYYY]权限[QA]。");
            List<DisplayMessage> displayMessages = new ArrayList<>();
            displayMessages.add(displayMessage1);
            displayMessages.add(displayMessage2);
            ReaderResponse readerResponse = new ReaderResponse("DD9AE815A3098F67E053BB4110ACAFC4", true, "", "OP", displayMessages);
//            ReaderResponse readerResponse = new ReaderResponse();
            return JSON.toJSONString(readerResponse);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/apkList")
    public List<YearData> apkList() {
        List<YearData> result = new ArrayList<>();
        String projectPath = System.getProperty("user.dir");
        Path rootPath = Paths.get(projectPath + "/apk");

        try {
            // 检查根目录是否存在并且是一个目录
            if (!Files.exists(rootPath) || !Files.isDirectory(rootPath)) {
                log.warn("根目录 {} 不存在或不是一个目录", rootPath);
                return result;
            }

            // 遍历年份目录
            Files.list(rootPath).filter(Files::isDirectory).forEach(yearDir -> {
                String year = yearDir.getFileName().toString();
                YearData yearData = new YearData();
                yearData.setYear(Integer.parseInt(year));
                List<MonthData> monthList = new ArrayList<>();

                // 遍历月份目录
                try {
                    Files.list(yearDir).filter(Files::isDirectory).forEach(monthDir -> {
                        String month = monthDir.getFileName().toString();
                        MonthData monthData = new MonthData();
                        monthData.setMonth(month);
                        List<String> apkFiles = new ArrayList<>();

                        // 遍历APK文件
                        try {
                            Files.list(monthDir).forEach(apkFile -> {
                                apkFiles.add(0, apkFile.getFileName().toString()); // 直接添加所有文件
                            });

                            // 如果月份目录下有APK文件，则添加到月份数据中
                            if (!apkFiles.isEmpty()) {
                                monthData.setFileList(apkFiles);
                                monthList.add(monthData);
                            }
                        } catch (IOException e) {
                            log.error("遍历月份目录 {} 下的文件时发生错误", monthDir, e);
                        }
                    });
                } catch (IOException e) {
                    log.error("遍历年份目录 {} 下的文件时发生错误", yearDir, e);
                }

                // 如果年份目录下有月份数据，则添加到年份数据中
                if (!monthList.isEmpty()) {
                    yearData.setMonthList(monthList);
                    result.add(yearData);
                }
            });
        } catch (IOException e) {
            log.error("遍历根目录 {} 时发生错误", rootPath, e);
        }
        result.sort(Comparator.comparingInt(YearData::getYear).reversed());
        result.forEach(yearData -> {
            yearData.getMonthList().sort(Comparator.comparing(MonthData::getMonth).reversed());
        });
        return result;
    }


    @GetMapping("operateAndroid")
    public AjaxResult operateAndroid(String type) {
        // type: 1:截屏，2:重启，3:关机，4:关闭屏幕，5：音量控制+，6：音量控制-，7：返回，8：home键
        return AjaxResult.success();
    }

    @PostMapping("deleteApk")
    public AjaxResult deleteApk(@RequestParam String path, @RequestParam(required = false) String apkName) {
        String projectPath = System.getProperty("user.dir");
        Path basePath = Paths.get(projectPath, path);

        try {
            if (StringUtils.isEmpty(apkName)) {
                // 删除整个文件夹及其所有内容
                if (Files.exists(basePath) && Files.isDirectory(basePath)) {
                    FileUtils.deleteDirectory(basePath.toFile());
                    apkService.deleteDirectoryByPath(basePath.toString());
                    return AjaxResult.success("文件夹删除成功");
                } else {
                    return AjaxResult.error("文件夹不存在");
                }
            } else {
                // 删除指定文件
                Path filePath = basePath.resolve(apkName);
                if (Files.exists(filePath) && Files.isRegularFile(filePath)) {
                    apkService.deleteDirectoryFile(filePath.toString());
                    Files.delete(filePath);
                    return AjaxResult.success("文件删除成功");
                } else {
                    return AjaxResult.error("文件不存在");
                }
            }
        } catch (Exception e) {
            log.error("删除操作失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }

}
