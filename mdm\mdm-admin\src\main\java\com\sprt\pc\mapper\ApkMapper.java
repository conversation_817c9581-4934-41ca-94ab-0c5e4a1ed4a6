package com.sprt.pc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.dto.ApkDTO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * apk信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Repository
public interface ApkMapper extends BaseMapper<Apk> {
    /**
     * 查询apk信息
     *
     * @param id apk信息主键
     * @return apk信息
     */
    public Apk selectApkById(Long id);

    /**
     * 查询apk信息列表
     *
     * @param apk apk信息
     * @return apk信息集合
     */
    public List<Apk> selectApkList(Apk apk);

    /**
     * 新增apk信息
     *
     * @param apk apk信息
     * @return 结果
     */
    public int insertApk(Apk apk);

    /**
     * 修改apk信息
     *
     * @param apk apk信息
     * @return 结果
     */
    public int updateApk(Apk apk);

    /**
     * 删除apk信息
     *
     * @param id apk信息主键
     * @return 结果
     */
    public int deleteApkById(Long id);

    /**
     * 批量删除apk信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApkByIds(Long[] ids);

    /**
     * 根据文件路径查询apk信息
     * @param filePath
     * @return
     */
    Apk selectApkByFilePath(String filePath);

    List<Apk> selectApkListByPath(ApkDTO apkDTO);

    List<Apk> selectApkByApkId(List<String> apkIds);
}