package com.sprt.pc.controller;

import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import com.sprt.pc.domain.ActivityCode;
import com.sprt.pc.service.ActivityCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 注册码Controller
 * 
 * <AUTHOR>
 * @date 2025-05-07
 */
@RestController
@RequestMapping("/pc/activityCode")
public class ActivityCodeController extends BaseController
{
    @Autowired
    private ActivityCodeService activityCodeService;

    /**
     * 查询注册码列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ActivityCode activityCode)
    {
        startPage();
        List<ActivityCode> list = activityCodeService.selectActivityCodeList(activityCode);
        return getDataTable(list);
    }

    /**
     * 导出注册码列表
     */
    @Log(title = "注册码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ActivityCode activityCode)
    {
        List<ActivityCode> list = activityCodeService.selectActivityCodeList(activityCode);
        ExcelUtil<ActivityCode> util = new ExcelUtil<ActivityCode>(ActivityCode.class);
        util.exportExcel(response, list, "注册码数据");
    }

    /**
     * 获取注册码详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(activityCodeService.selectActivityCodeById(id));
    }

    /**
     * 新增注册码
     */
    @Log(title = "注册码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ActivityCode activityCode)
    {
        return toAjax(activityCodeService.insertActivityCode(activityCode));
    }

    /**
     * 修改注册码
     */
    @Log(title = "注册码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ActivityCode activityCode)
    {
        return toAjax(activityCodeService.updateActivityCode(activityCode));
    }

    /**
     * 删除注册码
     */
    @Log(title = "注册码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(activityCodeService.deleteActivityCodeByIds(ids));
    }

    @GetMapping("getExpireActivityCode")
    public AjaxResult getExpireActivityCode() throws Exception {
    	return success(activityCodeService.getExpireActivityCode());
    }
}
