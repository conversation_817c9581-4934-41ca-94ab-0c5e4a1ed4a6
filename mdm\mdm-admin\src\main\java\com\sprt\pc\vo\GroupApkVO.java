package com.sprt.pc.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组与apk绑定对象 tb_group_apk
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_group_apk")
public class GroupApkVO {
    private static final long serialVersionUID = 1L;

    /**
     * 组id
     */
    private Long groupId;


    @Excel(name = "组名")
    private String groupName;

    /**
     * apkid
     */
    private Long apkId;


    @Excel(name = "apk名称")
    private String apkName;

    private String apkImg;

    private String packageName;

    private String filePath;

    private String version;

}
