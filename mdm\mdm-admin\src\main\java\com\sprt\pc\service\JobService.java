package com.sprt.pc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.android.vo.JobVO;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.Job;
import com.sprt.pc.vo.DeviceVO;

import java.util.List;

/**
 * 定时推送Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface JobService extends IService<Job> {
    /**
     * 查询定时推送
     *
     * @param id 定时推送主键
     * @return 定时推送
     */
    public JobVO selectJobById(Long id);

    /**
     * 查询定时推送列表
     *
     * @param job 定时推送
     * @return 定时推送集合
     */
    public List<JobVO> selectJobList(Job job);

    /**
     * 新增定时推送
     *
     * @param job 定时推送
     * @return 结果
     */
    public int insertJob(Job job);

    /**
     * 修改定时推送
     *
     * @param job 定时推送
     * @return 结果
     */
    public int updateJob(Job job);

    /**
     * 批量删除定时推送
     *
     * @param ids 需要删除的定时推送主键集合
     * @return 结果
     */
    public int deleteJobByIds(Long[] ids);

    /**
     * 删除定时推送信息
     *
     * @param id 定时推送主键
     * @return 结果
     */
    public int deleteJobById(Long id);

    /**
     * 查询定时推送设备列表
     * @param jobId
     * @return
     */
    List<DeviceVO> getDeviceByJobId(Long jobId);

    /**
     * 查询定时推送apk列表
     * @param jobId
     * @return
     */
    List<Apk> getApkByJobId(Long jobId);
}