<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.pc.mapper.DeviceMapper">

    <resultMap type="Device" id="DeviceResult">
        <result property="id" column="id"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceId" column="device_id"/>
        <result property="sn" column="sn"/>
        <result property="mac" column="mac"/>
        <result property="ip" column="ip"/>
        <result property="model" column="model"/>
        <result property="platform" column="platform"/>
        <result property="version" column="version"/>
        <result property="assetCode" column="asset_code"/>
        <result property="rssi" column="rssi"/>
        <result property="wifiName" column="wifi_name"/>
        <result property="battery" column="battery"/>
        <result property="location" column="location"/>
        <result property="activityTime" column="activity_time"/>
        <result property="apMac" column="ap_mac"/>
        <result property="isRecharge" column="is_recharge"/>
        <result property="remainingStorage" column="remaining_storage"/>
        <result property="status" column="status"/>
        <result property="groupId" column="group_id"/>
        <result property="heartbeatTime" column="heartbeat_time"/>
        <result property="apkIds" column="apk_ids"/>
        <result property="createTime" column="create_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectDeviceVo">
        select id,
               device_name,
               device_id,
               sn,
               mac,
               ip,
               model,
               platform,
               version,
               asset_code,
               rssi,
               ap_mac,
               is_recharge,
               wifi_name,
               battery,
               location,
               activity_time,
               remaining_storage,
               status,
               group_id,
               remark,
               heartbeat_time,
               apk_ids,
               create_time
        from tb_device
    </sql>

    <select id="selectDeviceList" parameterType="Device" resultType="com.sprt.pc.vo.DeviceVO">
        select
        d.id,
        d.mac,
        d.sn,
        d.ip,
        d.model,
        d.platform,
        d.version,
        d.status,
        d.group_id,
        g.group_name,
        d.asset_code,
        d.ap_mac,
        d.is_recharge,
        d.rssi,
        d.wifi_name,
        d.battery,
        d.location,
        d.activity_time,
        d.remaining_storage,
        d.device_id,
        d.remark,
        d.heartbeat_time,
        d.create_time
        from tb_device d
        left join tb_group g on d.group_id = g.id
        <where>
            <if test="mac != null  and mac != ''">and mac = #{mac}</if>
            <if test="sn != null  and sn != ''">and sn = #{sn}</if>
            <if test="ip != null  and ip != ''">and ip = #{ip}</if>
            <if test="model != null  and model != ''">and model = #{model}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="platform != null and platform != ''">and platform = #{platform}</if>
            <if test="version != null and version != ''">and version = #{version}</if>
            <if test="groupId != null ">and group_id = #{groupId}</if>
            <if test="assetCode != null  and assetCode != ''">and asset_code = #{assetCode}</if>
            <if test="deviceId != null  and deviceId != ''">and device_id = #{deviceId}</if>
        </where>
    </select>

    <select id="selectDeviceById" parameterType="Long" resultMap="DeviceResult">
        select d.id,
               d.mac,
               d.sn,
               d.ip,
               d.model,
               d.platform,
               d.version,
               d.status,
               d.group_id,
               g.group_name,
               d.asset_code,
               d.rssi,
               d.ap_mac,
               d.is_recharge,
               d.wifi_name,
               d.battery,
               d.location,
               d.activity_time,
               d.remaining_storage,
               d.device_id,
               d.remark,
               d.heartbeat_time,
               d.create_time
        from tb_device d
                 left join tb_group g on d.group_id = g.id
        where d.id = #{id}
    </select>

    <insert id="insertDevice" parameterType="Device">
        insert into tb_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="sn != null">sn,</if>
            <if test="mac != null">mac,</if>
            <if test="ip != null">ip,</if>
            <if test="model != null">model,</if>
            <if test="platform != null">platform,</if>
            <if test="version != null">version,</if>
            <if test="assetCode != null">asset_code,</if>
            <if test="apMac != null">ap_mac,</if>
            <if test="isRecharge != null">is_recharge,</if>
            <if test="rssi != null">rssi,</if>
            <if test="wifiName != null">wifi_name,</if>
            <if test="battery != null">battery,</if>
            <if test="location != null">location,</if>
            <if test="activityTime != null">activity_time,</if>
            <if test="remainingStorage != null">remaining_storage,</if>
            <if test="status != null">status,</if>
            <if test="groupId != null">group_id,</if>
            <if test="remark != null">remark,</if>
            <if test="heartbeatTime != null">heartbeat_time,</if>
            <if test="apkIds != null">apk_ids,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="sn != null">#{sn},</if>
            <if test="mac != null">#{mac},</if>
            <if test="ip != null">#{ip},</if>
            <if test="model != null">#{model},</if>
            <if test="platform != null">#{platform},</if>
            <if test="version != null">#{version},</if>
            <if test="assetCode != null">#{assetCode},</if>
            <if test="apMac != null">#{apMac},</if>
            <if test="isRecharge != null">#{isRecharge},</if>
            <if test="rssi != null">#{rssi},</if>
            <if test="wifiName != null">#{wifiName},</if>
            <if test="battery != null">#{battery},</if>
            <if test="location != null">#{location},</if>
            <if test="activityTime != null">#{activityTime},</if>
            <if test="remainingStorage != null">#{remainingStorage},</if>
            <if test="status != null">#{status},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="heartbeatTime != null">#{heartbeatTime},</if>
            <if test="apkIds != null">#{apkIds},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateDevice" parameterType="Device">
        update tb_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="sn != null">sn = #{sn},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="model != null">model = #{model},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="version != null">version = #{version},</if>
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="rssi != null">rssi = #{rssi},</if>
            <if test="wifiName != null">wifi_name = #{wifiName},</if>
            <if test="battery != null">battery = #{battery},</if>
            <if test="location != null">location = #{location},</if>
            <if test="activityTime != null">activity_time = #{activityTime},</if>
            <if test="apMac != null">ap_mac = #{apMac},</if>
            <if test="isRecharge != null">is_recharge = #{isRecharge},</if>
            <if test="remainingStorage != null">remaining_storage = #{remainingStorage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="heartbeatTime != null">heartbeat_time = #{heartbeatTime},</if>
            <if test="apkIds != null">apk_ids = #{apkIds},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceById" parameterType="Long">
        delete
        from tb_device
        where id = #{id}
    </delete>

    <delete id="deleteDeviceByIds" parameterType="String">
        delete from tb_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateDeviceBySn">
        update tb_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="assetCode != null">asset_code = #{assetCode},</if>
            <if test="ip != null and ip != ''">ip = #{ip},</if>
            <if test="mac != null and mac != ''">mac = #{mac},</if>
            <if test="platform != null and platform != ''">platform = #{platform},</if>
            <if test="version != null and version != ''">version = #{version},</if>
            <if test="rssi != null">rssi = #{rssi},</if>
            <if test="wifiName != null">wifi_name = #{wifiName},</if>
            <if test="battery != null">battery = #{battery},</if>
            <if test="activityTime != null">activity_time = #{activityTime},</if>
            <if test="apMac != null">ap_mac = #{apMac},</if>
            <if test="isRecharge != null">is_recharge = #{isRecharge},</if>
            <if test="remainingStorage != null">remaining_storage = #{remainingStorage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="heartbeatTime != null">heartbeat_time = #{heartbeatTime},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
        </trim>
        <where>
            <if test="sn != null">and sn = #{sn}</if>
            <if test="sn == null or sn == ''">
                <if test="mac != null and mac != ''">and mac = #{mac}</if>
            </if>
            <!--            <if test="(mac == null or mac == '') and (sn == null or sn == '')">-->
            <!--                <if test="deviceId != null">and device_id = #{deviceId}</if>-->
            <!--            </if>-->

            <!--            <if test="mac != null and mac !=''">and mac = #{mac}</if>-->
            <!--            <if test="mac == null or mac == ''">-->
            <!--                <if test="sn != null and sn != ''">and sn = #{sn}</if>-->
            <!--            </if>-->
            <!--            <if test="(mac == null or mac == '') and (sn == null or sn == '')">-->
            <!--                <if test="deviceId != null">and device_id = #{deviceId}</if>-->
            <!--            </if>-->
            and status &lt; 2
        </where>
    </update>
    <select id="selectDeviceVOList" resultType="com.sprt.pc.vo.DeviceVO">
        select
        d.id,
        d.mac,
        d.sn,
        d.ip,
        d.model,
        d.status,
        d.platform,
        d.version,
        d.ap_mac,
        d.is_recharge,
        d.group_id,
        g.group_name,
        d.asset_code,
        d.device_name,
        d.device_id,
        d.remark,
        d.heartbeat_time,
        d.create_time,
        d.rssi,
        d.wifi_name,
        d.battery,
        d.location,
        d.activity_time,
        d.remaining_storage
        from tb_device d
        left join tb_group g on d.group_id = g.id
        <where>
            <if test="groupIds != null">
                d.group_id in
                <foreach collection="groupIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="where != null and where != ''">
                and (d.mac like concat('%',#{where},'%')
                or d.sn like concat('%',#{where},'%')
                or d.ip like concat('%',#{where},'%')
                or d.model like concat('%',#{where},'%')
                or d.platform like concat('%',#{where},'%')
                or d.version like concat('%',#{where},'%')
                or d.asset_code like concat('%',#{where},'%')
                or d.device_id like concat('%',#{where},'%')
                or d.remark like concat('%',#{where},'%')
                or d.status like concat('%',#{where},'%')
                or d.battery like concat('%',#{where},'%'))
            </if>
            <if test="status != null">
                <if test="status == 2">
                    and d.status &lt; #{status}
                </if>
                <if test="status != 2">
                    and d.status = #{status}
                </if>
            </if>
            <if test="battery != null">and d.battery &lt;= #{battery}</if>
        </where>
    </select>
    <select id="selectDeviceListByMacs" resultType="com.sprt.pc.vo.DeviceVO">
        select
        d.id,
        d.mac,
        d.sn,
        d.ip,
        d.model,
        d.status,
        d.platform,
        d.version,
        d.ap_mac,
        d.is_recharge,
        d.group_id,
        g.group_name,
        d.asset_code,
        d.device_name,
        d.device_id,
        d.remark,
        d.heartbeat_time,
        d.create_time,
        d.rssi,
        d.wifi_name,
        d.battery,
        d.location,
        d.activity_time,
        d.remaining_storage
        from tb_device d
        left join tb_group g on d.group_id = g.id
        where d.mac in
        <foreach item="mac" collection="macs" open="(" separator="," close=")">
            #{mac}
        </foreach>
    </select>
    <select id="selectDeviceByDeviceIds" resultType="com.sprt.pc.vo.DeviceVO">
        select
        d.id,
        d.mac,
        d.sn,
        d.ip,
        d.model,
        d.platform,
        d.version,
        d.status,
        d.group_id,
        g.group_name,
        d.asset_code,
        d.ap_mac,
        d.is_recharge,
        d.rssi,
        d.wifi_name,
        d.battery,
        d.location,
        d.activity_time,
        d.remaining_storage,
        d.device_id,
        d.remark,
        d.heartbeat_time,
        d.create_time
        from tb_device d
        left join tb_group g on d.group_id = g.id
        where d.id in
        <foreach item="deviceId" collection="list" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>
</mapper>