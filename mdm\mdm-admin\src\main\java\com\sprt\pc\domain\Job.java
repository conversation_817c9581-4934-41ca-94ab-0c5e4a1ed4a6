package com.sprt.pc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 定时推送对象 tb_job
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_job")
public class Job {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /**
     * 所有需要推送的设备id使用,间隔
     */
    @Excel(name = "所有需要推送的设备id使用,间隔")
    private String deviceIds;

    /**
     * 选中apk的所有id使用，间隔
     */
    @Excel(name = "选中apk的所有id使用，间隔")
    private String apkIds;

    /**
     * 0不覆盖，1覆盖
     */
    @Excel(name = "0不覆盖，1覆盖")
    private Long checkedPackage;

    /**
     * 推送状态0待执行，1已执行，2执行失败
     */
    @Excel(name = "推送状态0待执行，1已执行，2执行失败")
    private Long status;

    private String remark;

    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Long createBy;

    private Integer isEnable;
}
