package com.sprt.android.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.mapper.OperationLogMapper;
import com.sprt.android.log.service.OperationLogService;
import com.sprt.android.log.vo.OperationLogVO;
import com.sprt.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {
    @Autowired
    private OperationLogMapper operationLogMapper;

    /**
     * 查询操作日志
     *
     * @param id 操作日志主键
     * @return 操作日志
     */
    @Override
    public OperationLog selectOperationLogById(Long id) {
        return operationLogMapper.selectOperationLogById(id);
    }

    /**
     * 查询操作日志列表
     *
     * @param operationLog 操作日志
     * @return 操作日志
     */
    @Override
    public List<OperationLogVO> selectOperationLogList(OperationLog operationLog) {
        return operationLogMapper.selectOperationLogList(operationLog);
    }

    /**
     * 新增操作日志
     *
     * @param operationLog 操作日志
     * @return 结果
     */
    @Override
    public int insertOperationLog(OperationLog operationLog) {
        operationLog.setCreateTime(DateUtils.getNowDate());
        return operationLogMapper.insert(operationLog);
    }

    /**
     * 修改操作日志
     *
     * @param operationLog 操作日志
     * @return 结果
     */
    @Override
    public int updateOperationLog(OperationLog operationLog) {
        return operationLogMapper.updateOperationLog(operationLog);
    }

    /**
     * 批量删除操作日志
     *
     * @param ids 需要删除的操作日志主键
     * @return 结果
     */
    @Override
    public int deleteOperationLogByIds(Long[] ids) {
        return operationLogMapper.deleteOperationLogByIds(ids);
    }

    /**
     * 删除操作日志信息
     *
     * @param id 操作日志主键
     * @return 结果
     */
    @Override
    public int deleteOperationLogById(Long id) {
        return operationLogMapper.deleteOperationLogById(id);
    }

    @Override
    public List<OperationLogVO> getOperationLogByDeviceId(Long deviceId) {
        return operationLogMapper.selectOperationLogByDeviceId(deviceId);
    }
}