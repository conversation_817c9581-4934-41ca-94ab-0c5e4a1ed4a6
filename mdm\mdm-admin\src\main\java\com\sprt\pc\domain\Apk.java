package com.sprt.pc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * apk信息对象 tb_apk
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_apk")
public class Apk
{
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /** apk图标 */
    @Excel(name = "apk图标" )
    private String apkImage;

    /** apk名称 */
    @Excel(name = "apk名称" )
    private String apkName;

    /** apk包名 */
    @Excel(name = "apk包名" )
    private String apkPackage;

    /** apk文件存储路径 */
    @Excel(name = "apk文件存储路径" )
    private String filePath;

    /** 安装成功设备数量 */
    @Excel(name = "安装成功设备数量" )
    private Long num;

    /** 版本号 */
    @Excel(name = "版本号" )
    private String version;

    /** versioncode */
    @Excel(name = "versioncode" )
    private String versionCode;

    /** 描述 */
    @Excel(name = "描述" )
    private String description;

    /** 文件存放路径 */
    @Excel(name = "文件存放路径" )
    private String apkPath;

    @Excel(name = "上传时间" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
