package com.sprt.pc.controller;

import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import com.sprt.pc.domain.GroupApk;
import com.sprt.pc.dto.GroupApkDTO;
import com.sprt.pc.service.GroupApkService;
import com.sprt.pc.vo.GroupApkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 组与apk绑定Controller
 * 
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/pc/groupApk")
public class GroupApkController extends BaseController
{
    @Autowired
    private GroupApkService groupApkService;

    /**
     * 查询组与apk绑定列表
     */
    @GetMapping("/list")
    public TableDataInfo list(GroupApk groupApk)
    {
        startPage();
        List<GroupApkVO> list = groupApkService.selectGroupApkList(groupApk);
        return getDataTable(list);
    }

    /**
     * 导出组与apk绑定列表
     */
    @Log(title = "组与apk绑定", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GroupApk groupApk)
    {
        List<GroupApkVO> list = groupApkService.selectGroupApkList(groupApk);
        ExcelUtil<GroupApkVO> util = new ExcelUtil<GroupApkVO>(GroupApkVO.class);
        util.exportExcel(response, list, "组与apk绑定数据");
    }

    /**
     * 获取组与apk绑定详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(groupApkService.selectGroupApkById(id));
    }

    /**
     * 新增组与apk绑定
     */
    @Log(title = "组与apk绑定", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GroupApkDTO groupApkDTO)
    {
        return toAjax(groupApkService.insertGroupApk(groupApkDTO));
    }

    /**
     * 修改组与apk绑定
     */
    @Log(title = "组与apk绑定", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GroupApk groupApk)
    {
        return toAjax(groupApkService.updateGroupApk(groupApk));
    }

    /**
     * 删除组与apk绑定
     */
    @Log(title = "组与apk绑定", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(groupApkService.deleteGroupApkByIds(ids));
    }

    @GetMapping("getApkImg/{packageName}/{version}")
    public AjaxResult getApkImg(@PathVariable String packageName,@PathVariable String version)
    {
    	return success(groupApkService.getApkImg(packageName, version));
    }
}
