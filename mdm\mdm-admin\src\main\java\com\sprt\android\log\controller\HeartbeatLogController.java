package com.sprt.android.log.controller;

import com.sprt.android.log.domain.HeartbeatLog;
import com.sprt.android.log.service.HeartbeatLogService;
import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心跳日志Controller
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/log/log")
public class HeartbeatLogController extends BaseController
{
    @Autowired
    private HeartbeatLogService heartbeatLogService;

    /**
     * 查询心跳日志列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HeartbeatLog heartbeatLog)
    {
        startPage();
        List<HeartbeatLog> list = heartbeatLogService.selectHeartbeatLogList(heartbeatLog);
        return getDataTable(list);
    }

    /**
     * 导出心跳日志列表
     */
    @Log(title = "心跳日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HeartbeatLog heartbeatLog)
    {
        List<HeartbeatLog> list = heartbeatLogService.selectHeartbeatLogList(heartbeatLog);
        ExcelUtil<HeartbeatLog> util = new ExcelUtil<HeartbeatLog>(HeartbeatLog.class);
        util.exportExcel(response, list, "心跳日志数据");
    }

    /**
     * 获取心跳日志详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(heartbeatLogService.selectHeartbeatLogById(id));
    }

    /**
     * 新增心跳日志
     */
    @Log(title = "心跳日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HeartbeatLog heartbeatLog)
    {
        return toAjax(heartbeatLogService.insertHeartbeatLog(heartbeatLog));
    }

    /**
     * 修改心跳日志
     */
    @Log(title = "心跳日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HeartbeatLog heartbeatLog)
    {
        return toAjax(heartbeatLogService.updateHeartbeatLog(heartbeatLog));
    }

    /**
     * 删除心跳日志
     */
    @Log(title = "心跳日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(heartbeatLogService.deleteHeartbeatLogByIds(ids));
    }
}
