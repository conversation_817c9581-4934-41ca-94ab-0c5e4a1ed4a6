package com.sprt.android.listen.websocket;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;

public class WebSocketClientApp extends WebSocketClient {

    public WebSocketClientApp(URI serverUri) {
        super(serverUri);
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        System.out.println("与 Android 端连接成功!");
    }

    @Override
    public void onMessage(String message) {
        System.out.println("收到来自 Android 端的消息: " + message);
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("连接关闭: " + reason);
    }

    @Override
    public void onError(Exception ex) {
        ex.printStackTrace();
    }

    // 发送消息给 Android 端
    public void sendControlMessage(int x, int y) {
        String message = String.format("{\"type\":\"tap\",\"x\":%d,\"y\":%d}", x, y);
        send(message);
    }

    public static void main(String[] args) throws Exception {
        // WebSocket 服务器地址，指向 Android 端的 WebSocket 服务器地址
        WebSocketClientApp client = new WebSocketClientApp(new URI("ws://************:9088")); // 请确保 Android 设备的 IP 地址正确
        client.connect();

        // 模拟发送点击坐标
        client.sendControlMessage(300, 500); // 发送模拟点击消息

    }
}
