package com.sprt.android.domain.rfid;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReaderResponse {

    // 请求消息中的MessageId
    @JsonProperty("MessageId")
    private String MessageId;

    // true-成功false-失败
    @JsonProperty("IsSuccess")
    private Boolean IsSuccess;

    // 消息内容，IsSuccess为false时的报错信息
    @JsonProperty("MessageContent")
    private String MessageContent;

    // 角色
    @JsonProperty("RoleName")
    private String RoleName;

    // 气泡显示内容
    @JsonProperty("DisplayMessages")
    private List<DisplayMessage> DisplayMessages;
}
