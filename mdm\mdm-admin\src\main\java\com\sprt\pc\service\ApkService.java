package com.sprt.pc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.dto.ApkDTO;
import com.sprt.pc.vo.DeviceVO;

import java.util.List;
import java.util.Map;

/**
 * apk信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface ApkService extends IService<Apk> {
    /**
     * 查询apk信息
     *
     * @param id apk信息主键
     * @return apk信息
     */
    public Apk selectApkById(Long id);

    /**
     * 查询apk信息列表
     *
     * @param apk apk信息
     * @return apk信息集合
     */
    public List<Apk> selectApkList(Apk apk);

    /**
     * 新增apk信息
     *
     * @param apk apk信息
     * @return 结果
     */
    public int insertApk(Apk apk);

    /**
     * 修改apk信息
     *
     * @param apk apk信息
     * @return 结果
     */
    public int updateApk(Apk apk);

    /**
     * 批量删除apk信息
     *
     * @param ids 需要删除的apk信息主键集合
     * @return 结果
     */
    public int deleteApkByIds(Long[] ids);

    /**
     * 删除apk信息信息
     *
     * @param id apk信息主键
     * @return 结果
     */
    public int deleteApkById(Long id);

    /**
     * 根据文件路径查询apk信息
     *
     * @param filePath
     * @return
     */
    Apk selectApkByFilePath(String filePath);

    /**
     * 根据项目路径查询apk信息
     *
     * @param apkDTO
     * @return
     */
    List<Apk> selectApkListByPath(ApkDTO apkDTO);

    /**
     * 根据文件路径删除文件夹
     *
     * @param toString
     */
    void deleteDirectoryByPath(String toString);

    /**
     * 根据文件路径删除文件
     *
     * @param toString
     */
    void deleteDirectoryFile(String toString);

    /**
     * 获取所有安装该apk的设备
     * @param params
     * @return
     */
    List<DeviceVO> getInstallDevice(Map<String, String> params);

    /**
     * 根据apkid查询apk信息
     * @param apkIds
     * @return
     */
    List<Apk> getApkByApkId(List<String> apkIds);
}