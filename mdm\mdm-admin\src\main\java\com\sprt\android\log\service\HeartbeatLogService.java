package com.sprt.android.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.android.log.domain.HeartbeatLog;

import java.util.List;

/**
 * 心跳日志Service接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface HeartbeatLogService extends IService<HeartbeatLog> {
    /**
     * 查询心跳日志
     *
     * @param id 心跳日志主键
     * @return 心跳日志
     */
    public HeartbeatLog selectHeartbeatLogById(Long id);

    /**
     * 查询心跳日志列表
     *
     * @param heartbeatLog 心跳日志
     * @return 心跳日志集合
     */
    public List<HeartbeatLog> selectHeartbeatLogList(HeartbeatLog heartbeatLog);

    /**
     * 新增心跳日志
     *
     * @param heartbeatLog 心跳日志
     * @return 结果
     */
    public int insertHeartbeatLog(HeartbeatLog heartbeatLog);

    /**
     * 修改心跳日志
     *
     * @param heartbeatLog 心跳日志
     * @return 结果
     */
    public int updateHeartbeatLog(HeartbeatLog heartbeatLog);

    /**
     * 批量删除心跳日志
     *
     * @param ids 需要删除的心跳日志主键集合
     * @return 结果
     */
    public int deleteHeartbeatLogByIds(Long[] ids);

    /**
     * 删除心跳日志信息
     *
     * @param id 心跳日志主键
     * @return 结果
     */
    public int deleteHeartbeatLogById(Long id);

    /**
     * 查询apk安装数量
     *
     * @return
     */
    public void selectApkCount();
}