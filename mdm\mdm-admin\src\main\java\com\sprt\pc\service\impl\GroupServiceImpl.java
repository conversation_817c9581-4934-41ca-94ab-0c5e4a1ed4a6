package com.sprt.pc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sprt.android.dto.GroupUpdateDTO;
import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.utils.MyWebSocketClient;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.Device;
import com.sprt.pc.domain.Group;
import com.sprt.pc.dto.DeviceGroupDTO;
import com.sprt.pc.mapper.ApkMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupMapper;
import com.sprt.pc.service.GroupService;
import com.sprt.pc.vo.GroupVO;
import org.java_websocket.handshake.HandshakeImpl1Server;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.util.*;

/**
 * 分组Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Service
public class GroupServiceImpl extends ServiceImpl<GroupMapper, Group> implements GroupService {

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private ApkMapper apkMapper;

    /**
     * 查询分组
     *
     * @param id 分组主键
     * @return 分组
     */
    @Override
    public Group selectGroupById(Long id) {
        return groupMapper.selectGroupById(id);
    }

    /**
     * 查询分组列表
     *
     * @param group 分组
     * @return 分组
     */
    @Override
    public List<GroupVO> selectGroupList(Group group) {
        List<GroupVO> groupVOList = new ArrayList<>();
        List<Group> groupList = groupMapper.selectGroupList(group);
        Map<Long, Group> tempMap = new HashMap<>();
        List<Group> childrenGroupList = new ArrayList<>();
        for (Group resGroup : groupList) {
            tempMap.put(resGroup.getId(), resGroup);
            String tempParentIds = resGroup.getParentIds() + "," + resGroup.getId();
            childrenGroupList.addAll(groupMapper.selectList(new LambdaQueryWrapper<Group>().like(Group::getParentIds, tempParentIds)));
        }
        for (Group group1 : childrenGroupList) {
            tempMap.put(group1.getId(), group1);
        }
        tempMap.forEach((key, value) -> {
            GroupVO groupVO = new GroupVO();
            BeanUtils.copyProperties(value, groupVO);
            // 根据apkIds查询全部apk
            if (StringUtils.isNotEmpty(value.getApkIds())) {
                List<String> apkIdList = Arrays.asList(value.getApkIds().split(","));
                List<Apk> apkList = apkMapper.selectList(new LambdaQueryWrapper<Apk>().in(Apk::getId, apkIdList));
                groupVO.setApkList(apkList);
            }
            groupVOList.add(groupVO);
        });

        return groupVOList;
    }

    /**
     * 新增分组
     *
     * @param group 分组
     * @return 结果
     */
    @Override
    public int insertGroup(Group group) {
        if (group.getParentId() != null) {
            Group parentGroup = groupMapper.selectGroupById(group.getParentId());

            group.setParentIds(parentGroup.getParentIds() + "," + parentGroup.getId());
        }
        if (StringUtils.isNotEmpty(group.getUserName())) {
            Long count = groupMapper.selectCount(new LambdaQueryWrapper<Group>().eq(Group::getUserName, group.getUserName()));
            if (count > 0) {
                throw new RuntimeException("账号已存在，不能重复");
            }
        }
        return groupMapper.insert(group);
    }

    /**
     * 修改分组
     *
     * @param group 分组
     * @return 结果
     */
    @Override
    @Transactional
    public int updateGroup(Group group) {
        Group oldGroup = groupMapper.selectGroupById(group.getId());
        if (!oldGroup.getGroupName().equals(group.getGroupName())) {
            Long count = groupMapper.selectCount(new LambdaQueryWrapper<Group>().eq(Group::getGroupName, group.getGroupName()));
            if (count > 0) {
                throw new RuntimeException("分组名称已存在");
            }
        }
        if (StringUtils.isNotEmpty(group.getUserName()) && !Objects.equals(oldGroup.getUserName(), group.getUserName())) {
            Long count = groupMapper.selectCount(new LambdaQueryWrapper<Group>().eq(Group::getUserName, group.getUserName()));
            if (count > 0) {
                throw new RuntimeException("账号已存在，不能重复");
            }
        }
        if (!Objects.equals(oldGroup.getParentId(), group.getParentId())) {
            Group parentGroup = groupMapper.selectGroupById(group.getParentId());

            group.setParentIds(parentGroup.getParentIds() + "," + parentGroup.getId());
        }
        // 查找全部当前节点的子集
        List<Group> children = groupMapper.selectList(new LambdaQueryWrapper<Group>().like(Group::getParentIds, group.getId()));
        for (Group child : children) {
            child.setParentIds(child.getParentIds().replace(oldGroup.getParentIds(), group.getId().toString()));
            groupMapper.updateGroup(child);
        }
        return groupMapper.updateGroup(group);
    }

    /**
     * 批量删除分组
     *
     * @param ids 需要删除的分组主键
     * @return 结果
     */
    @Override
    public int deleteGroupByIds(Long[] ids) {
        return groupMapper.deleteGroupByIds(ids);
    }

    /**
     * 删除分组信息
     *
     * @param id 分组主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteGroupById(Long id) {
        // 分组中有设备不允许删除
        List<Long> groupIds = new ArrayList<>();
        for (Group group : groupMapper.selectList(new LambdaQueryWrapper<Group>().like(Group::getParentIds, id))) {
            groupIds.add(group.getId());
        }
        groupIds.add(id);
        Long count = deviceMapper.selectCount(new LambdaQueryWrapper<Device>().in(Device::getGroupId, groupIds));
        if (count > 0) {
            throw new RuntimeException("该分组或该分组子级分组中有设备，禁止删除！");
        }
        int updateCount = 0;
        for (Long groupId : groupIds) {
            groupMapper.deleteGroupById(groupId);
            updateCount++;
        }
        return updateCount == groupIds.size() ? 1 : 0;
    }

    @Override
    @Transactional
    public int updateDeviceGroup(DeviceGroupDTO deviceGroupDTO) {
        // 获取新组信息
        Group newGroup = groupMapper.selectGroupById(Long.parseLong(deviceGroupDTO.getGroupId()));

        if (newGroup == null) {
            return 0;
        }
        String[] deviceIdArray = deviceGroupDTO.getDeviceId().split(",");

        for (String deviceId : deviceIdArray) {
            threadPoolTaskExecutor.execute(() -> {
                OperationLog operationLog = new OperationLog();
                operationLog.setType("修改分组");
                operationLog.setSubdivisionType("修改分组");
                operationLog.setIsSuccess("成功");
                // 查询设备信息
                Device device = deviceMapper.selectDeviceById(Long.parseLong(deviceId));

                if (device == null) {
                    return;
                }

                // 向android设备发起更新
                URI uri = null; // 根据你的服务地址修改
                MyWebSocketClient client = null;
                try {
                    uri = new URI("ws://" + device.getIp() + ":5008");
                    client = new MyWebSocketClient(uri);
                    client.connectBlocking();
                    client.onOpen(new HandshakeImpl1Server());
                    ArrayList<String> groupList = new ArrayList<>();
                    GroupUpdateDTO groupUpdateDTO = new GroupUpdateDTO();
                    groupUpdateDTO.setType("server");
                    groupUpdateDTO.setValue("setUsername");
                    groupList.add(newGroup.getUserName());
                    groupList.add(newGroup.getPassword());
                    groupUpdateDTO.setValueList(groupList);
                    GroupUpdateDTO[] groupUpdateDTOArr = new GroupUpdateDTO[1];
                    groupUpdateDTOArr[0] = groupUpdateDTO;
                    client.send(JSON.toJSONString(groupUpdateDTOArr));
                    // 发送成功后修改数据库数据
                    device.setGroupId(newGroup.getId());
                    deviceMapper.updateDevice(device);
                    operationLog.setIsSuccess("成功");
                    operationLog.setContent("修改成功");


                } catch (Exception e) {
                    operationLog.setIsSuccess("失败");
                    operationLog.setContent("修改成功，错误原因：" + e);
                    log.error("连接异常", e);
                } finally {
                    if (client != null) {
                        client.close();
                    }
                }
            });
        }
        return 1;
    }
}