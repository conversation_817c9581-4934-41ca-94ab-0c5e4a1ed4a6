<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket代理测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.sent {
            color: #007bff;
        }
        .log-entry.received {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.info {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>WebSocket代理测试页面</h1>
    
    <div class="container">
        <h2>连接配置</h2>
        <div class="form-group">
            <label for="serverUrl">服务器WebSocket地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/proxy-websocket" placeholder="ws://localhost:8080/proxy-websocket">
        </div>
        <div class="form-group">
            <label for="deviceIp">设备IP地址:</label>
            <input type="text" id="deviceIp" value="*************" placeholder="************* 或 *************:8080">
        </div>
        <button id="connectBtn" onclick="connect()">连接</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
    </div>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
    </div>
    
    <div class="container">
        <h2>发送消息</h2>
        <div class="form-group">
            <label for="messageInput">消息内容 (JSON格式):</label>
            <textarea id="messageInput" rows="4" placeholder='{"command": "getData", "params": {"key": "value"}}'></textarea>
        </div>
        <button id="sendBtn" onclick="sendMessage()" disabled>发送消息</button>
        <button onclick="sendTestMessage()">发送测试消息</button>
    </div>
    
    <div class="container">
        <h2>消息日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = connected ? 'status connected' : 'status disconnected';
            isConnected = connected;
            
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('sendBtn').disabled = !connected;
        }

        function addLogEntry(message, type = 'info') {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            const timestamp = new Date().toLocaleTimeString();
            entry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function connect() {
            const serverUrl = document.getElementById('serverUrl').value;
            if (!serverUrl) {
                alert('请输入服务器WebSocket地址');
                return;
            }

            try {
                ws = new WebSocket(serverUrl);
                
                ws.onopen = function() {
                    updateStatus('已连接到代理服务器', true);
                    addLogEntry('WebSocket连接已建立', 'info');
                };

                ws.onmessage = function(event) {
                    addLogEntry(`收到消息: ${event.data}`, 'received');
                    
                    // 尝试解析JSON消息
                    try {
                        const data = JSON.parse(event.data);
                        if (data.error) {
                            addLogEntry(`错误: ${data.error}`, 'error');
                        }
                    } catch (e) {
                        // 不是JSON格式的消息，直接显示
                    }
                };

                ws.onerror = function(error) {
                    addLogEntry(`WebSocket错误: ${error}`, 'error');
                };

                ws.onclose = function() {
                    updateStatus('连接已断开', false);
                    addLogEntry('WebSocket连接已关闭', 'info');
                };

            } catch (error) {
                addLogEntry(`连接失败: ${error.message}`, 'error');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            if (!isConnected) {
                alert('请先连接到服务器');
                return;
            }

            const messageInput = document.getElementById('messageInput');
            const deviceIp = document.getElementById('deviceIp').value;
            let message = messageInput.value.trim();

            if (!message) {
                alert('请输入消息内容');
                return;
            }

            if (!deviceIp) {
                alert('请输入设备IP地址');
                return;
            }

            try {
                // 尝试解析为JSON
                let messageObj = JSON.parse(message);
                // 添加deviceIp字段
                messageObj.deviceIp = deviceIp;
                message = JSON.stringify(messageObj);
            } catch (e) {
                // 如果不是JSON格式，创建一个包含deviceIp的JSON对象
                message = JSON.stringify({
                    deviceIp: deviceIp,
                    data: message
                });
            }

            ws.send(message);
            addLogEntry(`发送消息: ${message}`, 'sent');
        }

        function sendTestMessage() {
            const deviceIp = document.getElementById('deviceIp').value;
            if (!deviceIp) {
                alert('请输入设备IP地址');
                return;
            }

            const testMessage = {
                deviceIp: deviceIp,
                command: 'ping',
                timestamp: new Date().toISOString(),
                data: 'Hello from WebSocket Proxy Test'
            };

            document.getElementById('messageInput').value = JSON.stringify(testMessage, null, 2);
            sendMessage();
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时设置默认消息
        window.onload = function() {
            const defaultMessage = {
                command: 'getData',
                params: {
                    type: 'status',
                    timestamp: new Date().toISOString()
                }
            };
            document.getElementById('messageInput').value = JSON.stringify(defaultMessage, null, 2);
        };
    </script>
</body>
</html>
