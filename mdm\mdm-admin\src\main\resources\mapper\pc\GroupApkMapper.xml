<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.pc.mapper.GroupApkMapper">

    <resultMap type="GroupApk" id="GroupApkResult">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="apkId" column="apk_id"/>
    </resultMap>

    <sql id="selectGroupApkVo">
        select g.id as group_id,a.apk_image as apk_img,a.apk_package as package_name,a.id as apk_id, g.group_name, a.apk_name, a.file_path
        from tb_group_apk ga
                 left join tb_group g on ga.group_id = g.id
                 left join tb_apk a on ga.apk_id = a.id
    </sql>

    <select id="selectGroupApkList" parameterType="GroupApk" resultType="com.sprt.pc.vo.GroupApkVO">
        select g.id as group_id,a.apk_image as apk_img,a.apk_package as package_name,a.id as apk_id,a.version as version, g.group_name, a.apk_name, a.file_path
        from tb_group_apk ga
        left join tb_group g on ga.group_id = g.id
        left join tb_apk a on ga.apk_id = a.id
        <where>
            <if test="groupId != null ">and ga.group_id = #{groupId}</if>
            <if test="apkId != null ">and ga.apk_id = #{apkId}</if>
        </where>
    </select>

    <select id="selectGroupApkById" parameterType="Long" resultType="com.sprt.pc.vo.GroupApkVO">
        <include refid="selectGroupApkVo"/>
        where ga.id = #{id}
    </select>

    <insert id="insertGroupApk" parameterType="GroupApk">
        insert into tb_group_apk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="groupId != null">group_id,</if>
            <if test="apkId != null">apk_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="apkId != null">#{apkId},</if>
        </trim>
    </insert>

    <update id="updateGroupApk" parameterType="GroupApk">
        update tb_group_apk
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="apkId != null">apk_id = #{apkId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGroupApkById" parameterType="Long">
        delete
        from tb_group_apk
        where id = #{id}
    </delete>

    <delete id="deleteGroupApkByIds" parameterType="String">
        delete from tb_group_apk where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>