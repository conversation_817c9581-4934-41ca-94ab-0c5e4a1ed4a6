package com.sprt.pc.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket调试控制器
 * 用于检查WebSocket配置和连接状态
 */
@RestController
@RequestMapping("/api/websocket")
public class WebSocketDebugController {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketDebugController.class);
    
    @Autowired
    private WebSocketProxyHandler webSocketProxyHandler;
    
    /**
     * 检查WebSocket配置状态
     */
    @GetMapping("/status")
    public Map<String, Object> getWebSocketStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            status.put("proxyHandlerLoaded", webSocketProxyHandler != null);
            status.put("timestamp", System.currentTimeMillis());
            status.put("endpoints", new String[]{"/proxy-websocket"});
            status.put("message", "WebSocket代理服务正常运行");
            status.put("success", true);
            
            logger.info("WebSocket状态检查: 正常");
            
        } catch (Exception e) {
            status.put("success", false);
            status.put("error", e.getMessage());
            status.put("message", "WebSocket代理服务异常");
            
            logger.error("WebSocket状态检查失败: {}", e.getMessage(), e);
        }
        
        return status;
    }
    
    /**
     * 获取WebSocket连接信息
     */
//    @GetMapping("/info")
//    public Map<String, Object> getConnectionInfo() {
//        Map<String, Object> info = new HashMap<>();
//
//        info.put("supportedProtocols", new String[]{"ws", "wss"});
//        info.put("endpoints", Map.of(
//            "proxy", "/proxy-websocket",
//            "sockjs", "/proxy-websocket (with SockJS fallback)"
//        ));
//        info.put("features", new String[]{
//            "设备WebSocket代理",
//            "自动连接管理",
//            "消息双向转发",
//            "错误处理",
//            "连接状态监控"
//        });
//        info.put("defaultDevicePort", "8080");
//        info.put("defaultDevicePath", "/websocket");
//
//        return info;
//    }
    
    /**
     * 测试连接端点
     */
    @GetMapping("/test")
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以添加一些基本的连接测试逻辑
            result.put("success", true);
            result.put("message", "WebSocket端点可用");
            result.put("testTime", System.currentTimeMillis());
            result.put("instructions", new String[]{
                "1. 确保前端连接到: ws://localhost:8080/proxy-websocket",
                "2. 发送包含deviceIp字段的JSON消息",
                "3. 检查浏览器控制台和服务器日志"
            });
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
