package com.sprt.android.log.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 心跳日志对象 tb_heartbeat_log
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HeartbeatLogDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名
     */
    @Excel(name = "用户名")
    private String username;

    /**
     * 密码
     */
    @Excel(name = "密码")
    private String password;

    /**
     * mac
     */
    @Excel(name = "mac")
    private String mac;

    /**
     * ip
     */
    @Excel(name = "ip")
    private String ip;

    /**
     * 电池电量
     */
    @Excel(name = "电池电量")
    private String battery;

    /**
     * wifi名称
     */
    @Excel(name = "wifi名称")
    private String wifiName;

    /**
     * 信号强度
     */
    @Excel(name = "信号强度")
    private String rssi;

    /**
     * apMac
     */
    @Excel(name = "apMac")
    private String apMac;

    /**
     * 屏幕点亮状态
     */
    @Excel(name = "屏幕点亮状态")
    private String screenState;

    /**
     * 是否充电
     */
    @Excel(name = "是否充电")
    private String isRecharge;

    /**
     * 所有安装程序包名
     */
    @Excel(name = "所有安装程序包名")
    private String allPackage;

    @Excel(name = "心跳时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String sn;

    private String deviceName;

}
