package com.sprt.android.listen.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

import javax.websocket.EndpointConfig;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;

@Configuration
public class WebSocketConfig {
    // WebSocket 配置，Spring Boot 会自动检测并注册 WebSocket 服务端
    @Configuration
    public static class WebSocketServer {

        @ServerEndpoint("/ws/remoteControl")
        public static class RemoteControlWebSocket {

            // 连接打开时
            @OnOpen
            public void onOpen(Session session, EndpointConfig config) {
                System.out.println("与 Android 端的 WebSocket 连接已建立，ID：" + session.getId());
            }

            // 接收到 Android 端的消息时
            @OnMessage
            public void onMessage(String message, Session session) {
                System.out.println("收到消息: " + message);
                // 在这里解析来自 Android 端的消息并执行控制逻辑
                // 例如：处理 "tap" 类型的消息，传递 x, y 坐标
                // 你可以使用 JSON 解析并执行相应的操作
            }

            // 连接关闭时
            @OnClose
            public void onClose(Session session) {
                System.out.println("与 Android 端的 WebSocket 连接已关闭，ID：" + session.getId());
            }
        }
    }
    
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}
