package com.sprt.pc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.pc.domain.ActivityCode;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 注册码Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Repository
public interface ActivityCodeMapper extends BaseMapper<ActivityCode> {
    /**
     * 查询注册码
     *
     * @param id 注册码主键
     * @return 注册码
     */
    public ActivityCode selectActivityCodeById(Long id);

    /**
     * 查询注册码列表
     *
     * @param activityCode 注册码
     * @return 注册码集合
     */
    public List<ActivityCode> selectActivityCodeList(ActivityCode activityCode);

    /**
     * 新增注册码
     *
     * @param activityCode 注册码
     * @return 结果
     */
    public int insertActivityCode(ActivityCode activityCode);

    /**
     * 修改注册码
     *
     * @param activityCode 注册码
     * @return 结果
     */
    public int updateActivityCode(ActivityCode activityCode);

    /**
     * 删除注册码
     *
     * @param id 注册码主键
     * @return 结果
     */
    public int deleteActivityCodeById(Long id);

    /**
     * 批量删除注册码
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteActivityCodeByIds(Long[] ids);
}