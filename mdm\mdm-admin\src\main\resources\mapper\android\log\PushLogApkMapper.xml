<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.android.log.mapper.PushLogApkMapper">

    <resultMap type="PushLogApk" id="PushLogApkResult">
        <result property="id"    column="id"    />
        <result property="pushLogId"    column="push_log_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="apkIds"    column="apk_ids"    />
    </resultMap>

    <sql id="selectPushLogApkVo">
        select id, push_log_id, device_id, apk_ids from tb_push_log_apk
    </sql>

    <select id="selectPushLogApkList" parameterType="PushLogApk" resultMap="PushLogApkResult">
        <include refid="selectPushLogApkVo"/>
        <where>
            <if test="apkIds != null  and apkIds != ''"> and apk_ids = #{apkIds}</if>
        </where>
    </select>

    <select id="selectPushLogApkById" parameterType="Long" resultMap="PushLogApkResult">
        <include refid="selectPushLogApkVo"/>
        where id = #{id}
    </select>

    <insert id="insertPushLogApk" parameterType="PushLogApk">
        insert into tb_push_log_apk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pushLogId != null">push_log_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="apkIds != null and apkIds != ''">apk_ids,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pushLogId != null">#{pushLogId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="apkIds != null and apkIds != ''">#{apkIds},</if>
        </trim>
    </insert>

    <update id="updatePushLogApk" parameterType="PushLogApk">
        update tb_push_log_apk
        <trim prefix="SET" suffixOverrides=",">
            <if test="pushLogId != null">push_log_id = #{pushLogId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="apkIds != null and apkIds != ''">apk_ids = #{apkIds},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePushLogApkById" parameterType="Long">
        delete from tb_push_log_apk where id = #{id}
    </delete>

    <delete id="deletePushLogApkByIds" parameterType="String">
        delete from tb_push_log_apk where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>