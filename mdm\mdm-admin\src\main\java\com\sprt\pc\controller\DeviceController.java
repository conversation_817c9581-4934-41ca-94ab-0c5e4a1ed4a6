package com.sprt.pc.controller;

import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import com.sprt.pc.domain.Device;
import com.sprt.pc.dto.DeviceDTO;
import com.sprt.pc.service.DeviceService;
import com.sprt.pc.vo.DeviceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备Controller
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@RestController
@RequestMapping("/device/device")
public class DeviceController extends BaseController
{
    @Autowired
    private DeviceService deviceService;

    /**
     * 查询设备列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DeviceDTO deviceDTO)
    {
        List<DeviceVO> list = deviceService.selectDeviceList(deviceDTO);
        return getDataTable(list);
    }

    /**
     * 导出设备列表
     */
    @Log(title = "设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceDTO deviceDTO)
    {
        List<DeviceVO> list = deviceService.selectDeviceList(deviceDTO);
        ExcelUtil<DeviceVO> util = new ExcelUtil<DeviceVO>(DeviceVO.class);
        util.exportExcel(response, list, "设备数据");
    }

    /**
     * 获取设备详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deviceService.selectDeviceById(id));
    }

    /**
     * 新增设备
     */
    @Log(title = "设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Device device)
    {
        return toAjax(deviceService.insertDevice(device));
    }

    /**
     * 修改设备
     */
    @Log(title = "设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Device device)
    {
        return toAjax(deviceService.updateDevice(device));
    }

    /**
     * 删除设备
     */
    @Log(title = "设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceService.deleteDeviceByIds(ids));
    }
}
