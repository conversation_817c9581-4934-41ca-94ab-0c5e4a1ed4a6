package com.sprt.pc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.mapper.OperationLogMapper;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.ActivityCode;
import com.sprt.pc.domain.Device;
import com.sprt.pc.domain.Group;
import com.sprt.pc.mapper.ActivityCodeMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupMapper;
import com.sprt.pc.service.ChartService;
import com.sprt.pc.vo.ChartData;
import com.sprt.pc.vo.ChartGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class ChartServiceImpl implements ChartService {

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private ActivityCodeMapper activityCodeMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    public ChartData selectChartData() {
        // 查询所有设备
        List<Device> devices = deviceMapper.selectList(null);
        int online = 0;
        int heightPower = 0;
        Integer deviceSum = devices.size();
        List<Integer> onlineList = new ArrayList<>();
        List<Integer> lowPowerList = new ArrayList<>();
        onlineList.add(deviceSum);
        lowPowerList.add(deviceSum);
        for (Device device : devices) {
            if (device.getStatus() == 1) {
                online++;
            }
            if (StringUtils.isNotEmpty(device.getBattery()) && Integer.parseInt(device.getBattery().replace("%", "")) > 30) {
                heightPower++;
            }
        }
        onlineList.add(online);
        lowPowerList.add(devices.size() - heightPower);

        List<Integer> licenceList = new ArrayList<>();
        List<ActivityCode> list = activityCodeMapper.selectList(new LambdaQueryWrapper<ActivityCode>().ne(ActivityCode::getType, 1));
        Long activityDeviceCount = deviceMapper.selectCount(new LambdaQueryWrapper<Device>().ne(Device::getStatus, 2));
        licenceList.add(Math.toIntExact(activityDeviceCount));
        int normalNum = 0;
        for (ActivityCode activityCode : list) {
            if(StringUtils.isEmpty(activityCode.getDeviceSum())){
                continue;
            }
            normalNum += Integer.parseInt(activityCode.getDeviceSum());
        }
        licenceList.add(normalNum);
        return new ChartData(onlineList, lowPowerList, licenceList);
    }

    @Override
    public List<ChartGroup> getGroupDeviceNum() {
        List<ChartGroup> chartGroupList = new ArrayList<>();
        List<Group> groupList = groupMapper.selectList(null);
        for (Group group : groupList) {
            ChartGroup chartGroup = new ChartGroup();
            chartGroup.setId(group.getId());
            chartGroup.setGroupName(group.getGroupName());
            chartGroup.setParentId(group.getParentId());
            Long deviceNum = deviceMapper.selectCount(new LambdaQueryWrapper<Device>().eq(Device::getGroupId, group.getId()));
            chartGroup.setNum(deviceNum.intValue());
            chartGroupList.add(chartGroup);
        }
        return chartGroupList;
    }

    @Override
    public List<OperationLog> getPushAPKToday() {
        return operationLogMapper.selectList(new LambdaQueryWrapper<OperationLog>()
                .eq(OperationLog::getType,"安装指令")
                .eq(OperationLog::getSubdivisionType,"APK安装")
                .eq(OperationLog::getIsSuccess,"成功")
                .ge(OperationLog::getCreateTime, LocalDate.now().atStartOfDay()));
    }
}
