package com.sprt.android.listen.scheduled;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sprt.android.dto.FileInfo;
import com.sprt.android.dto.GroupUpdateDTO;
import com.sprt.android.log.domain.HeartbeatLog;
import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.dto.PushLogDTO;
import com.sprt.android.log.mapper.HeartbeatLogMapper;
import com.sprt.android.log.mapper.OperationLogMapper;
import com.sprt.android.log.service.OperationLogService;
import com.sprt.android.log.service.PushLogService;
import com.sprt.android.utils.MyWebSocketClient;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.Device;
import com.sprt.pc.domain.Job;
import com.sprt.pc.mapper.ApkMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupApkMapper;
import com.sprt.pc.mapper.JobMapper;
import com.sprt.pc.vo.DeviceVO;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.handshake.HandshakeImpl1Server;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class androidTask {

    // 线程池
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private HeartbeatLogMapper heartbeatLogMapper;

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Autowired
    private OperationLogService operationLogService;

    @Autowired
    private JobMapper jobMapper;

    @Autowired
    private GroupApkMapper groupApkMapper;

    @Autowired
    private ApkMapper apkMapper;

    @Autowired
    private PushLogService pushLogService;


    @Scheduled(cron = "0 */5 * * * ? ") // 每5分钟执行一次
    public void runTask() {
        // 任务逻辑
        List<DeviceVO> devices = deviceMapper.selectDeviceList(null);
        for (DeviceVO deviceVO : devices) {
            String ip = deviceVO.getIp();
            if (StringUtils.isNotEmpty(ip)) {
                Device device = new Device();
                BeanUtils.copyProperties(deviceVO, device);
                boolean isReachable = true;
                boolean isDelete = true;
                if (deviceVO.getHeartbeatTime() != null) {
                    isReachable = System.currentTimeMillis() - deviceVO.getHeartbeatTime().getTime() <= 1000 * 60 * 5;

                }

                if (!isReachable) {
                    device.setStatus(0);
                    deviceMapper.updateDevice(device);
                }
//                if (deviceVO.getHeartbeatTime() != null) {
//                    isDelete = System.currentTimeMillis() - deviceVO.getHeartbeatTime().getTime() <= 1000L * 60L * 60L * 24L * 30L * 3L;
//                }
//                if (!isDelete) {
//                    deviceMapper.deleteDeviceById(device.getId());
//                }
            }
        }
    }

    @Scheduled(cron = "0 0 0 * * ?") // 每天0点执行
//    @Scheduled(cron = "0 0 * * * ?") //每小时执行一次
    public void clearHeartbeatLog() {
        log.info("删除心跳日志执行");
        LambdaQueryWrapper<HeartbeatLog> queryWrapper = new LambdaQueryWrapper<>();
//        Date date = new Date(System.currentTimeMillis() - 1000L * 60L * 60L ); //删除一小时之前的日志
        Date date = new Date(System.currentTimeMillis() - 1000L * 60L * 60L * 24L); // 删除七天之前的日志
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formatted = sdf.format(date);
        queryWrapper.lt(HeartbeatLog::getCreateTime, formatted);
        heartbeatLogMapper.delete(queryWrapper);
    }

    @Scheduled(cron = "0 0 0 * * ?") // 每天0点执行
    public void clearOperateLog() {
        log.info("删除操作执行");
        LambdaQueryWrapper<OperationLog> queryWrapper = new LambdaQueryWrapper<>();
        Date date = new Date(System.currentTimeMillis() - 1000L * 60L * 60L * 24L * 30L); // 删除三十天之前的日志
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formatted = sdf.format(date);
        queryWrapper.lt(OperationLog::getCreateTime, formatted);
        operationLogMapper.delete(queryWrapper);
    }

    //    @Scheduled(cron = "0 0,30 * * * ? ") // 整30分钟执行一次
    @Scheduled(cron = "0 0,30 * * * ? ") // 整30分钟执行一次
    public void pushApk() {
        log.info("定时推送执行");
        // 查找是否有需要定时推送的设备
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 获取当前时间
        Date now = new Date();
        // 转换为 LocalDateTime
        LocalDateTime localNow1 = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        // 减5分钟后的时间
        LocalDateTime afterOneMinute1 = localNow1.minusMinutes(5);
        // 如果需要转回 Date 类型
        Date beforeOneMinuteAsDate1 = Date.from(afterOneMinute1.atZone(ZoneId.systemDefault()).toInstant());

        // 获取当前时间
        // 转换为 LocalDateTime
        LocalDateTime localNow2 = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        // 加5分钟后的时间
        LocalDateTime afterOneMinute2 = localNow2.plusMinutes(5);
        // 如果需要转回 Date 类型
        Date afterOneMinuteAsDate2 = Date.from(afterOneMinute2.atZone(ZoneId.systemDefault()).toInstant());

        log.info("当前时间：" + sdf.format(now));
        // 所有未执行并且需要执行的定时推送
        List<Job> jobs = jobMapper.selectList(new LambdaQueryWrapper<Job>().eq(Job::getStatus, 0).ge(Job::getPushTime, sdf.format(beforeOneMinuteAsDate1)).le(Job::getPushTime, sdf.format(afterOneMinuteAsDate2)));
//        List<Job> jobs = jobMapper.selectList(new LambdaQueryWrapper<Job>().eq(Job::getStatus, 0));
        if (jobs.isEmpty()) {
            return;
        }
        for (Job job : jobs) {
            PushLogDTO pushLogDTO = new PushLogDTO();
            pushLogDTO.setApkIds(job.getApkIds());
            pushLogDTO.setDeviceIds(job.getDeviceIds());
            pushLogDTO.setPushUser(job.getCreateBy());
            pushLogService.insertPushLog(pushLogDTO);
            // 获取组中所有的设备
            String[] deviceIdArray = job.getDeviceIds().split(",");
            String[] apkIdArray = job.getApkIds().split(",");
            List<Apk> apkList = apkMapper.selectList(new LambdaQueryWrapper<Apk>().in(Apk::getId, Arrays.asList(apkIdArray)));
            for (String deviceId : deviceIdArray) {
                Device device = deviceMapper.selectDeviceById(Long.parseLong(deviceId));
                if (device == null || StringUtils.isEmpty(device.getIp())) {
                    continue;
                }
                threadPoolTaskExecutor.execute(() -> {
                    OperationLog operationLog = new OperationLog();
                    operationLog.setType("安装指令");
                    operationLog.setSubdivisionType("APK安装");
                    operationLog.setIsSuccess("成功");
                    operationLog.setDeviceId(device.getId());
                    try {
                        serverPushApk(device.getIp(), apkList, job.getCheckedPackage());
                        job.setStatus(1L);
                        jobMapper.updateJob(job);
                        operationLog.setContent("发送指令成功");
                        operationLogService.insertOperationLog(operationLog);
                    } catch (Exception e) {
                        operationLog.setIsSuccess("失败");
                        log.error("设备ID：" + device.getId() + "推送apk失败", e);
                        operationLog.setContent("发送指令失败，错误原因：" + e);
                        operationLogService.insertOperationLog(operationLog);
                    }
                });
            }
        }
    }

    private void serverPushApk(String deviceIp, List<Apk> apkList, Long checkedPackage) throws Exception {
        // 连接android设备
        URI uri = new URI("ws://" + deviceIp + ":5008");
        MyWebSocketClient client = new MyWebSocketClient(uri);
        client.connectBlocking();
        client.onOpen(new HandshakeImpl1Server());

        GroupUpdateDTO groupUpdateDTO = new GroupUpdateDTO();
        groupUpdateDTO.setType("server");
        groupUpdateDTO.setValue("download");

        ArrayList<FileInfo> fileInfoList = new ArrayList<>();


        for (Apk apk : apkList) {
            FileInfo fileInfo = new FileInfo();
            String relativePath = null;
            String[] parts = apk.getFilePath().split("filePath");
            if (parts.length > 1) {
                relativePath = parts[1].replace("\\", "/");
            }
            if (StringUtils.isEmpty(relativePath)) {
                continue;
            }
            fileInfo.setFilePath(relativePath);
            fileInfo.setPackageName(apk.getApkPackage());
            fileInfo.setVersion(apk.getVersion());
            fileInfoList.add(fileInfo);
        }
        groupUpdateDTO.setFileInfoList(fileInfoList);
        groupUpdateDTO.setCheckedPackage(checkedPackage != 0L);
        ArrayList<GroupUpdateDTO> groupUpdateDTOS = new ArrayList<>();
        groupUpdateDTOS.add(groupUpdateDTO);
        log.info("发送数据：" + JSON.toJSONString(groupUpdateDTOS));
        client.send(JSON.toJSONString(groupUpdateDTOS));
        client.close();
    }
}
