package com.sprt.android.utils;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.UUID;

public class DeviceIdUtil {

    // 获取 MAC 地址（第一个可用的网卡）
    private static String getMacAddress() {
        try {
            for (NetworkInterface ni : Collections.list(NetworkInterface.getNetworkInterfaces())) {
                if (!ni.isLoopback() && ni.getHardwareAddress() != null) {
                    byte[] macBytes = ni.getHardwareAddress();
                    StringBuilder sb = new StringBuilder();
                    for (byte b : macBytes) {
                        sb.append(String.format("%02X", b));
                    }
                    return sb.toString();
                }
            }
        } catch (Exception ignored) {}
        return "UNKNOWN_MAC";
    }

    // 获取主机名
    private static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "UNKNOWN_HOST";
        }
    }

    // 获取系统盘（根路径）信息
    private static String getRootPath() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            return "C:\\";
        } else {
            return "/";
        }
    }

    // 生成通用设备唯一 ID（UUID）
    public static String generateDeviceId() {
        String mac = getMacAddress();
        String host = getHostName();
        String rootPath = getRootPath();

        // 拼接信息作为 UUID 的源
        String raw = mac + "_" + host + "_" + rootPath;
        return UUID.nameUUIDFromBytes(raw.getBytes(StandardCharsets.UTF_8)).toString();
    }
}
