package com.sprt.pc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 设备对象 tb_device
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_device")
public class Device {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /** 设备名称 */
    @Excel(name = "设备名称" )
    private String deviceName;

    /** 设备id */
    @Excel(name = "设备id" )
    private String deviceId;

    /** 设备sn */
    @Excel(name = "设备sn" )
    private String sn;

    /** mac地址 */
    @Excel(name = "mac地址" )
    private String mac;

    /** ip地址 */
    @Excel(name = "ip地址" )
    private String ip;

    /** 设备型号 */
    @Excel(name = "设备型号" )
    private String model;

    /** 平台 */
    @Excel(name = "平台" )
    private String platform;

    /** 版本 */
    @Excel(name = "版本" )
    private String version;

    /** 资产编号 */
    @Excel(name = "资产编号" )
    private String assetCode;

    /** wifi信号强度 */
    @Excel(name = "wifi信号强度" )
    private String rssi;

    /** wifi名称 */
    @Excel(name = "wifi名称" )
    private String wifiName;

    /** 电池电量 */
    @Excel(name = "电池电量" )
    private String battery;

    /** 位置 */
    @Excel(name = "位置" )
    private String location;

    /** 活跃时间 */
    @Excel(name = "活跃时间" )
    private String activityTime;

    /** apMac */
    @Excel(name = "apMac" )
    private String apMac;

    /** 充电状态 */
    @Excel(name = "充电状态" )
    private String isRecharge;

    /** 剩余存储 */
    @Excel(name = "剩余存储" )
    private String remainingStorage;

    /** 在线状态，0:离线，1:在线，2:未激活 */
    @Excel(name = "在线状态，0:离线，1:在线，2:未激活" )
    private Integer status;

    /** 分组id */
    @Excel(name = "分组id" )
    private Long groupId;

    /** 心跳时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "心跳时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date heartbeatTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 安装的apkids */
    @Excel(name = "安装的apkids" )
    private String apkIds;

    @Excel(name = "备注")
    private String remark;
}
