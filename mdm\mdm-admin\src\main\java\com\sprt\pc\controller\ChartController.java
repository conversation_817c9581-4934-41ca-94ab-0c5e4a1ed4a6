package com.sprt.pc.controller;

import com.sprt.common.core.domain.AjaxResult;
import com.sprt.pc.service.ChartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/chart")
public class ChartController {

    @Autowired
    private ChartService chartService;

    @GetMapping("/getChartData")
    public AjaxResult getChartData() {
        return AjaxResult.success(chartService.selectChartData());
    }

    @GetMapping("/getGroupDeviceNum")
    public AjaxResult getGroupDeviceNum() {
        return AjaxResult.success(chartService.getGroupDeviceNum());
    }

    @GetMapping("/getPushAPKToday")
    public AjaxResult getPushAPKToday() {
        return AjaxResult.success(chartService.getPushAPKToday());
    }
}
