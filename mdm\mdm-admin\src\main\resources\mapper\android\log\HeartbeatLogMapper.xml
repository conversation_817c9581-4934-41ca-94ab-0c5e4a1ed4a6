<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.android.log.mapper.HeartbeatLogMapper">

    <resultMap type="HeartbeatLog" id="HeartbeatLogResult">
        <result property="id"    column="id"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="mac"    column="mac"    />
        <result property="ip"    column="ip"    />
        <result property="battery"    column="battery"    />
        <result property="wifiName"    column="wifi_name"    />
        <result property="rssi"    column="rssi"    />
        <result property="apMac"    column="ap_mac"    />
        <result property="screenState"    column="screen_state"    />
        <result property="isRecharge"    column="is_recharge"    />
        <result property="allPackage"    column="all_package"    />
        <result property="createTime"    column="create_time"    />
        <result property="deviceName"    column="device_name"    />
        <result property="sn"    column="sn"    />
    </resultMap>

    <sql id="selectHeartbeatLogVo">
        select id, username, password, mac, ip, battery, wifi_name, rssi, ap_mac, screen_state, is_recharge, all_package, create_time, sn,device_name from tb_heartbeat_log
    </sql>

    <select id="selectHeartbeatLogList" parameterType="HeartbeatLog" resultMap="HeartbeatLogResult">
        <include refid="selectHeartbeatLogVo"/>
        <where>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="battery != null  and battery != ''"> and battery = #{battery}</if>
            <if test="wifiName != null  and wifiName != ''"> and wifi_name like concat('%', #{wifiName}, '%')</if>
            <if test="rssi != null  and rssi != ''"> and rssi = #{rssi}</if>
            <if test="apMac != null  and apMac != ''"> and ap_mac = #{apMac}</if>
            <if test="screenState != null "> and screen_state = #{screenState}</if>
            <if test="isRecharge != null "> and is_recharge = #{isRecharge}</if>
            <if test="allPackage != null  and allPackage != ''"> and all_package = #{allPackage}</if>
            <if test="sn != null  and sn != ''"> and sn = #{sn}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name = #{deviceName}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectHeartbeatLogById" parameterType="Long" resultMap="HeartbeatLogResult">
        <include refid="selectHeartbeatLogVo"/>
        where id = #{id}
    </select>

    <select id="selectLastHeartbeatLog" resultType="com.sprt.android.log.domain.HeartbeatLog">
        SELECT
            t.mac,
            t.all_package
        FROM
            tb_heartbeat_log t
                INNER JOIN ( SELECT mac, MAX( create_time ) AS max_create_time FROM tb_heartbeat_log GROUP BY mac ) AS grouped ON t.mac = grouped.mac
                AND t.create_time = grouped.max_create_time;
    </select>
    <select id="selectLastHeartbeatLogBySn" resultType="com.sprt.android.log.domain.HeartbeatLog">
        SELECT
            t.sn,
            t.all_package
        FROM
            tb_heartbeat_log t
        WHERE
            t.sn = #{sn}
        ORDER BY
            t.create_time DESC
        LIMIT 1;
    </select>
    <select id="selectLastHeartbeatLogsBySnList" resultType="com.sprt.android.log.domain.HeartbeatLog">
        SELECT
            t.sn,
            t.all_package
        FROM
            tb_heartbeat_log t
        WHERE
            t.sn IN
            <foreach item="item" collection="list" separator="," open="(" close=")">
                #{item}
            </foreach>
        ORDER BY
            t.create_time DESC
        LIMIT 1;
    </select>

    <insert id="insertHeartbeatLog" parameterType="HeartbeatLog">
        insert into tb_heartbeat_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="mac != null">mac,</if>
            <if test="ip != null">ip,</if>
            <if test="battery != null">battery,</if>
            <if test="wifiName != null">wifi_name,</if>
            <if test="rssi != null">rssi,</if>
            <if test="apMac != null">ap_mac,</if>
            <if test="screenState != null">screen_state,</if>
            <if test="isRecharge != null">is_recharge,</if>
            <if test="allPackage != null">all_package,</if>
            <if test="createTime != null">create_time,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="sn != null">sn,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="mac != null">#{mac},</if>
            <if test="ip != null">#{ip},</if>
            <if test="battery != null">#{battery},</if>
            <if test="wifiName != null">#{wifiName},</if>
            <if test="rssi != null">#{rssi},</if>
            <if test="apMac != null">#{apMac},</if>
            <if test="screenState != null">#{screenState},</if>
            <if test="isRecharge != null">#{isRecharge},</if>
            <if test="allPackage != null">#{allPackage},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="sn != null">#{sn},</if>
        </trim>
    </insert>

    <update id="updateHeartbeatLog" parameterType="HeartbeatLog">
        update tb_heartbeat_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="battery != null">battery = #{battery},</if>
            <if test="wifiName != null">wifi_name = #{wifiName},</if>
            <if test="rssi != null">rssi = #{rssi},</if>
            <if test="apMac != null">ap_mac = #{apMac},</if>
            <if test="screenState != null">screen_state = #{screenState},</if>
            <if test="isRecharge != null">is_recharge = #{isRecharge},</if>
            <if test="allPackage != null">all_package = #{allPackage},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="sn != null">sn = #{sn},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHeartbeatLogById" parameterType="Long">
        delete from tb_heartbeat_log where id = #{id}
    </delete>

    <delete id="deleteHeartbeatLogByIds" parameterType="String">
        delete from tb_heartbeat_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>