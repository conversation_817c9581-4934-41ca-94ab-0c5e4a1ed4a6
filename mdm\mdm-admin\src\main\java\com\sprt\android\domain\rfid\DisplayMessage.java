package com.sprt.android.domain.rfid;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DisplayMessage {

    // true-成功false-失败
    @JsonProperty("IsSuccess")
    private Boolean IsSuccess;

    // 1-黑色  2-红色
    @JsonProperty("MessageColor")
    private Integer MessageColor;

    // 消息内容
    @JsonProperty("MessageBody")
    private String MessageBody;
}
