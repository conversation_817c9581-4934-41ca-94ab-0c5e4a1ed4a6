package com.sprt.pc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.GroupApk;
import com.sprt.pc.dto.GroupApkDTO;
import com.sprt.pc.mapper.ApkMapper;
import com.sprt.pc.mapper.GroupApkMapper;
import com.sprt.pc.service.GroupApkService;
import com.sprt.pc.vo.GroupApkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 组与apk绑定Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class GroupApkServiceImpl extends ServiceImpl<GroupApkMapper, GroupApk> implements GroupApkService {
    @Autowired
    private GroupApkMapper groupApkMapper;

    @Autowired
    private ApkMapper apkMapper;

    /**
     * 查询组与apk绑定
     *
     * @param id 组与apk绑定主键
     * @return 组与apk绑定
     */
    @Override
    public List<GroupApkVO> selectGroupApkById(Long id) {
        GroupApk groupApk = new GroupApk();
        groupApk.setGroupId(id);
        return groupApkMapper.selectGroupApkList(groupApk);
    }

    /**
     * 查询组与apk绑定列表
     *
     * @param groupApk 组与apk绑定
     * @return 组与apk绑定
     */
    @Override
    public List<GroupApkVO> selectGroupApkList(GroupApk groupApk) {
        return groupApkMapper.selectGroupApkList(groupApk);
    }

    /**
     * 新增组与apk绑定
     *
     * @param groupApkDTO 组与apk绑定
     * @return 结果
     */
    @Override
    public int insertGroupApk(GroupApkDTO groupApkDTO) {
        groupApkMapper.delete(new LambdaQueryWrapper<GroupApk>().eq(GroupApk::getGroupId, groupApkDTO.getGroupId()));
        int sum = 0;
        if(groupApkDTO.getApkIdList().isEmpty()){
            return 1;
        }
        for (Long apkId : groupApkDTO.getApkIdList()) {
            GroupApk groupApk = new GroupApk(null, groupApkDTO.getGroupId(), apkId);
            sum += groupApkMapper.insert(groupApk);
        }
        return sum == groupApkDTO.getApkIdList().size()? 1 : 0;
    }

    /**
     * 修改组与apk绑定
     *
     * @param groupApk 组与apk绑定
     * @return 结果
     */
    @Override
    public int updateGroupApk(GroupApk groupApk) {
        return groupApkMapper.updateGroupApk(groupApk);
    }

    /**
     * 批量删除组与apk绑定
     *
     * @param ids 需要删除的组与apk绑定主键
     * @return 结果
     */
    @Override
    public int deleteGroupApkByIds(Long[] ids) {
        return groupApkMapper.deleteGroupApkByIds(ids);
    }

    /**
     * 删除组与apk绑定信息
     *
     * @param id 组与apk绑定主键
     * @return 结果
     */
    @Override
    public int deleteGroupApkById(Long id) {
        return groupApkMapper.deleteGroupApkById(id);
    }

    @Override
    public String getApkImg(String packageName, String version) {
        List<Apk> apks = apkMapper.selectList(new LambdaQueryWrapper<Apk>().eq(Apk::getApkPackage, packageName).eq(Apk::getVersion, version));
        return apks.get(0).getApkImage();
    }
}