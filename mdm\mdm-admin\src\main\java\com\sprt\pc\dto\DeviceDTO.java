package com.sprt.pc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDTO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * mac地址
     */
    @Excel(name = "mac地址")
    private String mac;

    /**
     * 设备sn
     */
    @Excel(name = "设备sn")
    private String sn;

    /**
     * ip地址
     */
    @Excel(name = "ip地址")
    private String ip;

    /**
     * 设备型号
     */
    @Excel(name = "设备型号")
    private String model;

    /**
     * 平台
     */
    private String platform;

    /**
     * 版本
     */
    private String version;

    /**
     * 在线状态，0:离线，1:在线，2:未激活
     */
    @Excel(name = "在线状态，0:离线，1:在线，2:未激活")
    private Integer status;

    /**
     * 分组id
     */
    @Excel(name = "分组id")
    private Long groupId;

    @Excel(name = "资产资产编号")
    private String assetCode;

    @Excel(name = "设备id")
    private String deviceId;

    @Excel(name = "备注")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date heartbeatTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String where;

    private Integer battery;
}
