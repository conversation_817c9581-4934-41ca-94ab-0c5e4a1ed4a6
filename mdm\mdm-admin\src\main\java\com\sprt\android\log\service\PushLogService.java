package com.sprt.android.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.android.log.domain.PushLog;
import com.sprt.android.log.dto.PushLogDTO;
import com.sprt.android.log.vo.PushLogVO;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.vo.DeviceVO;

import java.util.List;

/**
 * 推送日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface PushLogService extends IService<PushLog> {
    /**
     * 查询推送日志
     *
     * @param id 推送日志主键
     * @return 推送日志
     */
    public PushLog selectPushLogById(Long id);

    /**
     * 查询推送日志列表
     *
     * @param pushLog 推送日志
     * @return 推送日志集合
     */
    public List<PushLogVO> selectPushLogList(PushLog pushLog);

    /**
     * 新增推送日志
     *
     * @param pushLogDTO 推送日志
     * @return 结果
     */
    public int insertPushLog(PushLogDTO pushLogDTO);

    /**
     * 修改推送日志
     *
     * @param pushLog 推送日志
     * @return 结果
     */
    public int updatePushLog(PushLog pushLog);

    /**
     * 批量删除推送日志
     *
     * @param ids 需要删除的推送日志主键集合
     * @return 结果
     */
    public int deletePushLogByIds(Long[] ids);

    /**
     * 删除推送日志信息
     *
     * @param id 推送日志主键
     * @return 结果
     */
    public int deletePushLogById(Long id);

    List<DeviceVO> getDeviceByPushLogId(Long pushLogId);

    List<Apk> getPushApkByPushLogId(Long pushLogId);

    List<DeviceVO> getRemainingDeviceAndApk(Long pushLogId);
}