package com.sprt.pc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sprt.android.log.domain.HeartbeatLog;
import com.sprt.android.log.domain.PushLogApk;
import com.sprt.android.log.dto.PackageInfo;
import com.sprt.android.log.mapper.HeartbeatLogMapper;
import com.sprt.android.log.mapper.PushLogApkMapper;
import com.sprt.android.vo.JobVO;
import com.sprt.common.utils.DateUtils;
import com.sprt.common.utils.SecurityUtils;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.Job;
import com.sprt.pc.mapper.ApkMapper;
import com.sprt.pc.mapper.JobMapper;
import com.sprt.pc.service.ApkService;
import com.sprt.pc.service.DeviceService;
import com.sprt.pc.service.JobService;
import com.sprt.pc.vo.DeviceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 定时推送Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class JobServiceImpl extends ServiceImpl<JobMapper, Job> implements JobService {
    @Autowired
    private JobMapper jobMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ApkService apkService;

    @Autowired
    private HeartbeatLogMapper heartbeatLogMapper;

    @Autowired
    private ApkMapper apkMapper;

    @Autowired
    private PushLogApkMapper pushLogApkMapper;

    /**
     * 查询定时推送
     *
     * @param id 定时推送主键
     * @return 定时推送
     */
    @Override
    public JobVO selectJobById(Long id) {
        return jobMapper.selectJobById(id);
    }

    /**
     * 查询定时推送列表
     *
     * @param job 定时推送
     * @return 定时推送
     */
    @Override
    public List<JobVO> selectJobList(Job job) {
        List<JobVO> jobVOList = jobMapper.selectJobList(job);
        for (JobVO jobVO : jobVOList) {
            int installApkCount = 0;
            if (jobVO.getStatus() == 0 || jobVO.getIsEnable() == 0) {
                jobVO.setInstallSum(installApkCount);
                continue;
            }
            String[] apkIdArray = jobVO.getApkIds().split(",");
            List<Long> apkIdList = new ArrayList<>();
            for (String apkId : apkIdArray) {
                apkIdList.add(Long.valueOf(apkId));
            }

            List<DeviceVO> deviceByJobIdList = getDeviceByJobId(jobVO.getId());
            for (DeviceVO deviceVO : deviceByJobIdList) {
                HeartbeatLog heartbeatLog = heartbeatLogMapper.selectLastHeartbeatLogBySn(deviceVO.getSn());
                if (heartbeatLog == null) {
                    continue;
                }
                String allPackage = heartbeatLog.getAllPackage();
                if (StringUtils.isEmpty(allPackage)) {
                    continue;
                }
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    PackageInfo[] packageInfoArray = mapper.readValue(allPackage, PackageInfo[].class);
                    for (PackageInfo packageInfo : packageInfoArray) {
                        Apk apk = apkMapper.selectOne(new LambdaQueryWrapper<Apk>()
                                .eq(Apk::getApkPackage, packageInfo.getPackageName())
                                .eq(Apk::getVersion, packageInfo.getVersionName()));
                        if (apkIdList.contains(apk.getId())) {
                            installApkCount++;
                        }
                    }
                } catch (Exception e) {
                    log.error("解析失败");
                }
            }
            jobVO.setInstallSum(installApkCount);
        }
        return jobVOList;
    }

    /**
     * 新增定时推送
     *
     * @param job 定时推送
     * @return 结果
     */
    @Override
    public int insertJob(Job job) {
        job.setCreateTime(DateUtils.getNowDate());
        job.setCreateBy(SecurityUtils.getUserId());
        return jobMapper.insert(job);
    }

    /**
     * 修改定时推送
     *
     * @param job 定时推送
     * @return 结果
     */
    @Override
    public int updateJob(Job job) {
        return jobMapper.updateJob(job);
    }

    /**
     * 批量删除定时推送
     *
     * @param ids 需要删除的定时推送主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteJobByIds(Long[] ids) {
        pushLogApkMapper.delete(new LambdaQueryWrapper<PushLogApk>()
                .in(PushLogApk::getPushLogId, Arrays.asList(ids)));
        return jobMapper.deleteJobByIds(ids);
    }

    /**
     * 删除定时推送信息
     *
     * @param id 定时推送主键
     * @return 结果
     */
    @Override
    public int deleteJobById(Long id) {
        return jobMapper.deleteJobById(id);
    }

    @Override
    public List<DeviceVO> getDeviceByJobId(Long jobId) {
        JobVO jobVO = jobMapper.selectJobById(jobId);
        String[] deviceIdArray = jobVO.getDeviceIds().split(",");
        if (deviceIdArray.length == 0) {
            return new ArrayList<>();
        }
        List<DeviceVO> deviceVOList = deviceService.getDeviceByDeviceIds(Arrays.asList(deviceIdArray));
        if (jobVO.getIsEnable() == 0 || jobVO.getStatus() == 0) {
            return deviceVOList;
        }
        String[] apkIdArray = jobVO.getApkIds().split(",");
        List<Long> apkIdList = new ArrayList<>();
        for (String apkId : apkIdArray) {
            apkIdList.add(Long.valueOf(apkId));
        }
        for (DeviceVO device : deviceVOList) {
            List<Apk> apkList = new ArrayList<>();
            HeartbeatLog heartbeatLog = heartbeatLogMapper.selectLastHeartbeatLogBySn(device.getSn());
            if (heartbeatLog == null) {
                continue;
            }
            String allPackage = heartbeatLog.getAllPackage();
            if (StringUtils.isEmpty(allPackage)) {
                continue;
            }
            try {
                ObjectMapper mapper = new ObjectMapper();
                PackageInfo[] packageInfoArray = mapper.readValue(allPackage, PackageInfo[].class);
                for (PackageInfo packageInfo : packageInfoArray) {
                    Apk apk = apkMapper.selectOne(new LambdaQueryWrapper<Apk>()
                            .eq(Apk::getApkPackage, packageInfo.getPackageName())
                            .eq(Apk::getVersion, packageInfo.getVersionName()));
                    if (apkIdList.contains(apk.getId())) {
                        apkList.add(apk);
                    }
                }
                device.setApkList(apkList);
            } catch (Exception e) {
                log.error("解析失败");
            }
        }
        return deviceVOList;
    }

    @Override
    public List<Apk> getApkByJobId(Long jobId) {
        JobVO jobVO = jobMapper.selectJobById(jobId);
        String[] apkIdArray = jobVO.getApkIds().split(",");
        if (apkIdArray.length == 0) {
            return new ArrayList<>();
        }
        return apkService.getApkByApkId(Arrays.asList(apkIdArray));
    }
}