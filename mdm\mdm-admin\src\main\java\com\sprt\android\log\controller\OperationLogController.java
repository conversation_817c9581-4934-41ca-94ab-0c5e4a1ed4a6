package com.sprt.android.log.controller;

import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.service.OperationLogService;
import com.sprt.android.log.vo.OperationLogVO;
import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作日志Controller
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/operation/log")
public class OperationLogController extends BaseController
{
    @Autowired
    private OperationLogService operationLogService;

    /**
     * 查询操作日志列表
     */
    @GetMapping("/list")
    public TableDataInfo list(OperationLog operationLog)
    {
        startPage();
        List<OperationLogVO> list = operationLogService.selectOperationLogList(operationLog);
        return getDataTable(list);
    }

    /**
     * 导出操作日志列表
     */
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OperationLog operationLog)
    {
        List<OperationLogVO> list = operationLogService.selectOperationLogList(operationLog);
        ExcelUtil<OperationLogVO> util = new ExcelUtil<OperationLogVO>(OperationLogVO.class);
        util.exportExcel(response, list, "操作日志数据");
    }

    /**
     * 获取操作日志详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(operationLogService.selectOperationLogById(id));
    }

    /**
     * 新增操作日志
     */
    @Log(title = "操作日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OperationLog operationLog)
    {
        return toAjax(operationLogService.insertOperationLog(operationLog));
    }

    /**
     * 修改操作日志
     */
    @Log(title = "操作日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OperationLog operationLog)
    {
        return toAjax(operationLogService.updateOperationLog(operationLog));
    }

    /**
     * 删除操作日志
     */
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(operationLogService.deleteOperationLogByIds(ids));
    }

    @GetMapping("getOperationLogByDeviceId")
    public TableDataInfo getOperationLogByDeviceId(OperationLog operationLog)
    {
        startPage();
        List<OperationLogVO> list = operationLogService.getOperationLogByDeviceId(operationLog.getDeviceId());
        return getDataTable(list);
    }
}
