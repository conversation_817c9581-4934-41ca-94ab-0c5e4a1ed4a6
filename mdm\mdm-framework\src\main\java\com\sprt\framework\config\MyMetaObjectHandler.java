package com.sprt.framework.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.sprt.common.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component      //把这个类的对象交给spring管理
public class MyMetaObjectHandler implements MetaObjectHandler {

    //mp执行添加操作时这个方法执行
    @Override
    public void insertFill(MetaObject metaObject) {
        //根据名称设置属性值
        this.setFieldValByName("createTime",new Date(),metaObject);
        this.setFieldValByName("updateTime",new Date(),metaObject);
    }

    //mp执行更新操作时这个方法执行
    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime",new Date(),metaObject);
    }
}
