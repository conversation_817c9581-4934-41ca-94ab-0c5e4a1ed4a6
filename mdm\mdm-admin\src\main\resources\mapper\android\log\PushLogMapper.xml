<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.android.log.mapper.PushLogMapper">

    <resultMap type="PushLog" id="PushLogResult">
        <result property="id" column="id"/>
        <result property="pushUser" column="push_user"/>
        <result property="deviceIds" column="device_ids"/>
        <result property="apkIds" column="apk_ids"/>
        <result property="startTime" column="start_time"/>
        <result property="installApkCount" column="install_apk_count"/>
        <result property="installedApkCount" column="installed_apk_count"/>
        <result property="completionPercentage" column="completion_percentage"/>
        <result property="endTime" column="end_time"/>
        <result property="remainingCount" column="remaining_count"/>
    </resultMap>

    <sql id="selectPushLogVo">
        select id,
               push_user,
               device_ids,
               apk_ids,
               start_time,
               install_apk_count,
               installed_apk_count,
               completion_percentage,
               end_time,
               remaining_count
        from tb_push_log
    </sql>

    <select id="selectPushLogList" parameterType="PushLog" resultType="com.sprt.android.log.vo.PushLogVO">
        select p.id, u.nick_name, p.device_ids, p.apk_ids, p.start_time, p.install_apk_count, p.installed_apk_count,
        p.completion_percentage, p.end_time, p.remaining_count from tb_push_log p
        left join sys_user u on u.user_id = p.push_user
        <where>
            <if test="id != null ">and p.id = #{id}</if>
            <if test="pushUser != null ">and p.push_user = #{pushUser}</if>
            <if test="deviceIds != null  and deviceIds != ''">and p.device_ids = #{deviceIds}</if>
            <if test="apkIds != null  and apkIds != ''">and p.apk_ids = #{apkIds}</if>
            <if test="startTime != null ">and p.start_time = #{startTime}</if>
            <if test="installApkCount != null ">and p.install_apk_count = #{installApkCount}</if>
            <if test="installedApkCount != null ">and p.installed_apk_count = #{installedApkCount}</if>
            <if test="completionPercentage != null  and completionPercentage != ''">and p.completion_percentage =
                #{completionPercentage}
            </if>
            <if test="endTime != null ">and p.end_time = #{endTime}</if>
            <if test="remainingCount != null ">and p.remaining_count = #{remainingCount}</if>
        </where>
        order by p.start_time desc
    </select>

    <select id="selectPushLogById" parameterType="Long" resultMap="PushLogResult">
        <include refid="selectPushLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertPushLog" parameterType="PushLog">
        insert into tb_push_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="pushUser != null">push_user,</if>
            <if test="deviceIds != null">device_ids,</if>
            <if test="apkIds != null">apk_ids,</if>
            <if test="startTime != null">start_time,</if>
            <if test="installApkCount != null">install_apk_count,</if>
            <if test="installedApkCount != null">installed_apk_count,</if>
            <if test="completionPercentage != null">completion_percentage,</if>
            <if test="endTime != null">end_time,</if>
            <if test="remainingCount != null">remaining_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pushUser != null">#{pushUser},</if>
            <if test="deviceIds != null">#{deviceIds},</if>
            <if test="apkIds != null">#{apkIds},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="installApkCount != null">#{installApkCount},</if>
            <if test="installedApkCount != null">#{installedApkCount},</if>
            <if test="completionPercentage != null">#{completionPercentage},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="remainingCount != null">#{remainingCount},</if>
        </trim>
    </insert>

    <update id="updatePushLog" parameterType="PushLog">
        update tb_push_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="pushUser != null">push_user = #{pushUser},</if>
            <if test="deviceIds != null">device_ids = #{deviceIds},</if>
            <if test="apkIds != null">apk_ids = #{apkIds},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="installApkCount != null">install_apk_count = #{installApkCount},</if>
            <if test="installedApkCount != null">installed_apk_count = #{installedApkCount},</if>
            <if test="completionPercentage != null">completion_percentage = #{completionPercentage},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="remainingCount != null">remaining_count = #{remainingCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePushLogById" parameterType="Long">
        delete
        from tb_push_log
        where id = #{id}
    </delete>

    <delete id="deletePushLogByIds" parameterType="String">
        delete from tb_push_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>