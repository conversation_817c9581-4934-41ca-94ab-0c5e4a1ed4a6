package com.sprt.android.listen;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.sprt.android.log.domain.HeartbeatLog;
import com.sprt.android.log.dto.HeartbeatLogDTO;
import com.sprt.android.log.service.HeartbeatLogService;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.mapper.DeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class MonitorHeartbeat {
    private static final int PORT = 9902;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private HeartbeatLogService heartbeatLogService;

    @PostConstruct
    public void startServer() {
        executorService.submit(this::listenForClients);
    }

    private void listenForClients() {
        try (ServerSocket serverSocket = new ServerSocket(PORT)) {
            log.info("监听端口 " + PORT + "，等待客户端连接...");
            while (true) {
                Socket clientSocket = serverSocket.accept();
                log.info("客户端连接: " + clientSocket.getInetAddress());
                executorService.submit(() -> handleClient(clientSocket));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleClient(Socket clientSocket) {
        try (InputStream inputStream = new BufferedInputStream(clientSocket.getInputStream());
             ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

            byte[] data = new byte[10240];
            int bytesRead;
            while ((bytesRead = inputStream.read(data)) != -1) {
                buffer.write(data, 0, bytesRead);
                String message = buffer.toString("UTF-8");
                log.info("收到客户端消息: " + message);
                //根据收到的消息进行修改ip
                if (StringUtils.isNotEmpty(message)) {
                    JSONObject jsonObject = JSON.parseObject(message);
                    if (StringUtils.isEmpty(jsonObject.getString("username")) || StringUtils.isEmpty(jsonObject.getString("password"))) {
                        continue;
                    }
                    HeartbeatLogDTO heartbeatLogDTO = JSON.parseObject(message, HeartbeatLogDTO.class);
                    HeartbeatLog heartbeatLog = new HeartbeatLog();
                    BeanUtils.copyProperties(heartbeatLogDTO, heartbeatLog);
                    heartbeatLog.setScreenState("true".equals(heartbeatLogDTO.getScreenState()) ? 1 : 0);
                    heartbeatLog.setIsRecharge("true".equals(heartbeatLogDTO.getIsRecharge()) ? 1 : 0);
                    heartbeatLogService.insertHeartbeatLog(heartbeatLog);
//                    try (Socket socket = new Socket(jsonObject.getString("ip"), 9901);
//                         OutputStream out = socket.getOutputStream()) {
//                        out.write("你掉线了".getBytes(StandardCharsets.UTF_8));
//                        out.flush();
//                    } catch (Exception e) {
//                        throw new RuntimeException(e);
//                    }
                    deviceMapper.updateDeviceBySn(jsonObject.getString("mac"), jsonObject.getString("ip"), null, null, jsonObject.getString("sn"), jsonObject.getString("deviceId"), jsonObject.getString("platform"), jsonObject.getString("version"), jsonObject.getString("battery"), jsonObject.getString("rssi"), jsonObject.getString("wifiName"), 1, new Date(), jsonObject.getString("activityTime"), jsonObject.getString("remainingStorage"),jsonObject.getString("apMac"),jsonObject.getString("isRecharge"), jsonObject.getString("deviceName"));
                }
                buffer.reset();
            }
        } catch (Exception e) {
            log.info("客户端连接中断: " + clientSocket.getInetAddress());
            log.error("客户端连接中断", e);
        } finally {
            try {
                clientSocket.close();
            } catch (Exception ignored) {
            }
        }
    }

}
