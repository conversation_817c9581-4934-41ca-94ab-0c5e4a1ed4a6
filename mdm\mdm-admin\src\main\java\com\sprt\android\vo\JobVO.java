package com.sprt.android.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sprt.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobVO {
    /**  */
    private Long id;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "推送时间" , width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss" )
    private Date pushTime;

    /** 所有需要推送的设备id使用,间隔 */
    @Excel(name = "所有需要推送的设备id使用,间隔" )
    private String deviceIds;

    /** 选中apk的所有id使用，间隔 */
    @Excel(name = "选中apk的所有id使用，间隔" )
    private String apkIds;

    /** 0不覆盖，1覆盖 */
    @Excel(name = "是否覆盖" )
    private Long checkedPackage;

    /** 推送状态0待执行，1已执行，2执行失败 */
    @Excel(name = "推送状态" )
    private Long status;

    private String remark;

    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Excel(name = "创建人")
    private String nickName;

    @Excel(name = "是否启用")
    private Integer isEnable;

    @Excel(name = "安装总数")
    private Integer installSum;
}
