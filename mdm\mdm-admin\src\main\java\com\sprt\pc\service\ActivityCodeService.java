package com.sprt.pc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.pc.domain.ActivityCode;
import com.sprt.pc.vo.ExpireActivityCodeVO;

import java.util.List;

/**
 * 注册码Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface ActivityCodeService extends IService<ActivityCode> {
    /**
     * 查询注册码
     *
     * @param id 注册码主键
     * @return 注册码
     */
    public ActivityCode selectActivityCodeById(Long id);

    /**
     * 查询注册码列表
     *
     * @param activityCode 注册码
     * @return 注册码集合
     */
    public List<ActivityCode> selectActivityCodeList(ActivityCode activityCode);

    /**
     * 新增注册码
     *
     * @param activityCode 注册码
     * @return 结果
     */
    public int insertActivityCode(ActivityCode activityCode);

    /**
     * 修改注册码
     *
     * @param activityCode 注册码
     * @return 结果
     */
    public int updateActivityCode(ActivityCode activityCode);

    /**
     * 批量删除注册码
     *
     * @param ids 需要删除的注册码主键集合
     * @return 结果
     */
    public int deleteActivityCodeByIds(Long[] ids);

    /**
     * 删除注册码信息
     *
     * @param id 注册码主键
     * @return 结果
     */
    public int deleteActivityCodeById(Long id);

    List<ExpireActivityCodeVO> getExpireActivityCode() throws Exception;
}