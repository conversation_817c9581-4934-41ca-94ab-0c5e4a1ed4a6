package com.sprt.pc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.android.vo.JobVO;
import com.sprt.pc.domain.Job;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 定时推送Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Repository
public interface JobMapper extends BaseMapper<Job> {
    /**
     * 查询定时推送
     *
     * @param id 定时推送主键
     * @return 定时推送
     */
    public JobVO selectJobById(Long id);

    /**
     * 查询定时推送列表
     *
     * @param job 定时推送
     * @return 定时推送集合
     */
    public List<JobVO> selectJobList(Job job);

    /**
     * 新增定时推送
     *
     * @param job 定时推送
     * @return 结果
     */
    public int insertJob(Job job);

    /**
     * 修改定时推送
     *
     * @param job 定时推送
     * @return 结果
     */
    public int updateJob(Job job);

    /**
     * 删除定时推送
     *
     * @param id 定时推送主键
     * @return 结果
     */
    public int deleteJobById(Long id);

    /**
     * 批量删除定时推送
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJobByIds(Long[] ids);
}