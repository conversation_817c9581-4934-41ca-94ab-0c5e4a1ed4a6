package com.sprt.android.controller;

import com.sprt.android.dto.SendFileDTO;
import com.sprt.android.log.service.HeartbeatLogService;
import com.sprt.android.service.FileService;
import com.sprt.android.vo.FileDTO;
import com.sprt.common.config.RuoYiConfig;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.utils.StringUtils;
import com.sprt.common.utils.file.FileUploadUtils;
import com.sprt.common.utils.file.FileUtils;
import com.sprt.pc.config.ServerInfoUtil;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.dto.ApkDTO;
import com.sprt.pc.service.ApkService;
import com.sprt.pc.vo.DeviceVO;
import lombok.extern.slf4j.Slf4j;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import net.dongliu.apk.parser.bean.IconFace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("android/file")
public class FileController extends BaseController {


    @Autowired
    private FileService fileService;

    @Autowired
    private ApkService apkService;

    @Autowired
    private HeartbeatLogService heartbeatLogService;

    @GetMapping("/getInstallDevice")
    public List<DeviceVO> getInstallDevice(@RequestParam Map<String, String> params) {
        return apkService.getInstallDevice(params);
    }

    @PostMapping("/createFolder")
    public AjaxResult createFolder(@RequestParam String folderPath) {
        String projectPath = System.getProperty("user.dir").replace("\\","/");
        Path path = Paths.get(projectPath + folderPath);
        try {
            Files.createDirectories(path);
            return success("文件夹创建成功");
        } catch (IOException e) {
            e.printStackTrace();
            return error("文件夹创建失败: " + e.getMessage());
        }
    }

    @GetMapping("getApkListByPath")
    public TableDataInfo getApkListByPath(ApkDTO apkDTO) {
        startPage();
        heartbeatLogService.selectApkCount();
        List<Apk> apkList = apkService.selectApkListByPath(apkDTO);
        return getDataTable(apkList);
    }

    @GetMapping("/getFolderList")
    public AjaxResult getFolderList() {
        String projectPath = System.getProperty("user.dir").replace("\\","/");

        Path path = Paths.get(projectPath + "/filePath/");
        try {
            List<Object> fileTree = buildFileTree(path);
            System.out.println(fileTree);
            return success(fileTree);
        } catch (IOException e) {
            e.printStackTrace();
            return error("获取文件夹列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/pushApk")
    public AjaxResult pushApk(@RequestParam MultipartFile file,
                              @RequestParam String uploadPath,
                              @RequestParam(required = false) String apkPath) {
        // 判断文件是否为空
        if (file.isEmpty()) {
            return AjaxResult.error("上传的文件不能为空");
        }

        // 规范化路径，避免路径遍历攻击
        if(StringUtils.isNotEmpty(uploadPath)){
            uploadPath = uploadPath.replace("\\","/");
        }
        if(StringUtils.isNotEmpty(apkPath)){
            apkPath = apkPath.replace("\\","/");
        }
        Path normalizedPath = Paths.get(uploadPath).normalize();

        // 创建目录（如果不存在）
        File uploadDir = new File(normalizedPath.toString());
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        // 获取文件名并构建完整路径
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return error("文件名不能为空");
        }
        String fileName = System.getProperty("user.dir").replace("\\","/") + uploadPath + originalFilename;
        Path filePath = normalizedPath.resolve(fileName);
        Apk tempApk = apkService.selectApkByFilePath(fileName);
        if (tempApk != null)
            return AjaxResult.error("文件已存在");
        // 保存文件
        Path directoryPath = Paths.get(System.getProperty("user.dir").replace("\\","/"), uploadPath);
        Path destinationPath = directoryPath.resolve(originalFilename);

        String fileExtension = "";

        if (!StringUtils.isEmpty(originalFilename) && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        }
        // 不是apk文件
        ApkFile apkFile = null;
        try {
            log.info("fileExtension:"+fileExtension);
            if (!"apk".equals(fileExtension)) {
                // 上传文件
                uploadNotApkFile(file, destinationPath, filePath,apkPath);
                return success("文件上传成功");
            }
            file.transferTo(destinationPath.toFile());

            apkFile = new ApkFile(destinationPath.toFile());
            // ====== 解析 APK 文件 ======
            ApkMeta apkMeta = apkFile.getApkMeta();
            String packageName = apkMeta.getPackageName();
            String versionName = apkMeta.getVersionName();
            Long versionCode = apkMeta.getVersionCode();
            String apkName = file.getOriginalFilename();

            // 获取第一个图标
            List<IconFace> iconFiles = apkFile.getAllIcons();
            String base64Icon = null;
            if (!iconFiles.isEmpty()) {
                byte[] iconData = iconFiles.get(0).getData();
                base64Icon = Base64.getEncoder().encodeToString(iconData);
            }
            String fileName1 = "";
            if (StringUtils.isNotEmpty(base64Icon) && base64Icon != null) {
                if (StringUtils.isEmpty(apkName) || apkName == null) {
                    apkName = System.currentTimeMillis() + ".jpg";
                }
                MultipartFile multipartFile = FileUtils.base64ToMultipartFile(base64Icon, apkName.replace(".apk", ".jpg"));
                // 上传文件路径
                String filePath1 = RuoYiConfig.getUploadPath();
                // 上传并返回新文件名称
                fileName1 = FileUploadUtils.upload(filePath1, multipartFile);
            }
            Apk apk = apkService.selectApkByFilePath(String.valueOf(filePath));
            if (apk != null) {
                apkService.deleteDirectoryFile(String.valueOf(filePath));
            }
            apk = new Apk();
            apk.setApkImage(ServerInfoUtil.getIpAddress() + ":" + ServerInfoUtil.getPort() + fileName1);
            apk.setApkName(apkName);
            apk.setApkPackage(packageName);
            apk.setFilePath(String.valueOf(filePath).replace("\\", "/"));
            apk.setVersion(versionName);
            apk.setVersionCode(versionCode.toString());
            apkService.insertApk(apk);
            return AjaxResult.success("apk文件上传成功");
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("apk文件上传失败: 请检查文件名是否重复" + e.getMessage());
        } finally {
            try {
                if (apkFile != null) {
                    apkFile.close();
                }
            } catch (Exception e) {
                log.error("关闭ApkFile时出错", e);
            }
        }
    }

    public List<Object> buildFileTree(Path rootPath) throws IOException {
        List<Object> result = new ArrayList<>();

        if (!Files.exists(rootPath) || !Files.isDirectory(rootPath)) {
            return result;
        }

        Files.walkFileTree(rootPath, new SimpleFileVisitor<Path>() {
            private final Deque<Map<String, Object>> dirStack = new ArrayDeque<>();
            private final Deque<List<Object>> listStack = new ArrayDeque<>();

            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) {
                Map<String, Object> dirMap = new HashMap<>();
                List<Object> fileList = new ArrayList<>();
                dirMap.put(dir.getFileName().toString(), fileList);

                if (!listStack.isEmpty()) {
                    List<Object> parentList = listStack.peek();
                    if (parentList != null) {
                        parentList.add(dirMap);
                    }
                } else {
                    result.add(dirMap);
                }

                dirStack.push(dirMap);
                listStack.push(fileList);

                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                List<Object> currentList = listStack.peek();
                if (currentList != null) {
                    FileDTO fileDTO = new FileDTO();
                    fileDTO.setFileName(file.getFileName().toString());
                    Apk apk = apkService.selectApkByFilePath(String.valueOf(file));
                    if (apk != null) {
                        fileDTO.setImgPath(apk.getApkImage());
                    }
                    currentList.add(fileDTO);
                }
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) {
                dirStack.pop();
                listStack.pop();
                return FileVisitResult.CONTINUE;
            }
        });

        return result;
    }

    /**
     * 接收设备id列表、apk列表，是否覆盖，等参数连接android设备并进行推送apk
     */
    @PostMapping("sendMessageToAndroid")
    public AjaxResult sendMessageToAndroid(@RequestBody SendFileDTO sendFileDTO) {
        return success(fileService.sendMessageToAndroid(sendFileDTO));
    }

    private void uploadNotApkFile(MultipartFile file, Path destinationPath, Path filePath, String apkPath) {
        try {
            file.transferTo(destinationPath.toFile());
            String apkName = file.getOriginalFilename();

            Apk apk = apkService.selectApkByFilePath(String.valueOf(filePath));
            if (apk != null) {
                apkService.deleteDirectoryFile(String.valueOf(filePath));
            }
            apk = new Apk();
            apk.setApkName(apkName);
            apk.setFilePath(String.valueOf(filePath));
            apk.setApkPath(apkPath);
            apkService.insertApk(apk);
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }
}
