package com.sprt.pc.controller;

import com.sprt.android.vo.JobVO;
import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import com.sprt.pc.domain.Job;
import com.sprt.pc.service.JobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 定时推送Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/job/job")
public class JobController extends BaseController
{
    @Autowired
    private JobService jobService;

    /**
     * 查询定时推送列表
     */
    @PreAuthorize("@ss.hasPermi('job:job:list')")
    @GetMapping("/list")
    public TableDataInfo list(Job job)
    {
        startPage();
        List<JobVO> list = jobService.selectJobList(job);
        return getDataTable(list);
    }

    /**
     * 导出定时推送列表
     */
    @PreAuthorize("@ss.hasPermi('job:job:export')")
    @Log(title = "定时推送", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Job job)
    {
        List<JobVO> list = jobService.selectJobList(job);
        ExcelUtil<JobVO> util = new ExcelUtil<JobVO>(JobVO.class);
        util.exportExcel(response, list, "定时推送数据");
    }

    /**
     * 获取定时推送详细信息
     */
    @PreAuthorize("@ss.hasPermi('job:job:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jobService.selectJobById(id));
    }

    /**
     * 新增定时推送
     */
    @PreAuthorize("@ss.hasPermi('job:job:add')")
    @Log(title = "定时推送", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Job job)
    {
        return toAjax(jobService.insertJob(job));
    }

    /**
     * 修改定时推送
     */
    @PreAuthorize("@ss.hasPermi('job:job:edit')")
    @Log(title = "定时推送", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Job job)
    {
        return toAjax(jobService.updateJob(job));
    }

    /**
     * 删除定时推送
     */
    @PreAuthorize("@ss.hasPermi('job:job:remove')")
    @Log(title = "定时推送", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(jobService.deleteJobByIds(ids));
    }

    @GetMapping("getDeviceByJobId/{jobId}")
    public AjaxResult getDeviceByJobId(@PathVariable Long jobId)
    {
        return success(jobService.getDeviceByJobId(jobId));
    }

    @GetMapping("getApkByJobId/{jobId}")
    public AjaxResult getApkByJobId(@PathVariable Long jobId)
    {
        return success(jobService.getApkByJobId(jobId));
    }
}
