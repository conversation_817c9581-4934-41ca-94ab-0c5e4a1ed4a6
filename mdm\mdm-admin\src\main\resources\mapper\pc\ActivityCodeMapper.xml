<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.pc.mapper.ActivityCodeMapper">

    <resultMap type="ActivityCode" id="ActivityCodeResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="type"    column="type"    />
        <result property="createTime"    column="create_time"    />
        <result property="deviceSum"    column="device_sum"    />
    </resultMap>

    <sql id="selectActivityCodeVo">
        select id,code, expire_time,device_sum, type, create_time from tb_activity_code
    </sql>

    <select id="selectActivityCodeList" parameterType="ActivityCode" resultMap="ActivityCodeResult">
        <include refid="selectActivityCodeVo"/>
        <where>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
    </select>

    <select id="selectActivityCodeById" parameterType="Long" resultMap="ActivityCodeResult">
        <include refid="selectActivityCodeVo"/>
        where id = #{id}
    </select>

    <insert id="insertActivityCode" parameterType="ActivityCode">
        insert into tb_activity_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null">code,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="deviceSum != null">device_sum,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null">#{code},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="type != null">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="deviceSum != null">#{deviceSum},</if>
        </trim>
    </insert>

    <update id="updateActivityCode" parameterType="ActivityCode">
        update tb_activity_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="deviceSum != null">device_sum = #{deviceSum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteActivityCodeById" parameterType="Long">
        delete from tb_activity_code where id = #{id}
    </delete>

    <delete id="deleteActivityCodeByIds" parameterType="String">
        delete from tb_activity_code where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>