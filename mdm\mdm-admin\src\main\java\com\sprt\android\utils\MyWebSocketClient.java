package com.sprt.android.utils;

import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.function.Consumer;

@Slf4j
public class MyWebSocketClient extends WebSocketClient {

    private Consumer<String> messageListener;

    public MyWebSocketClient(URI serverUri) {
        super(serverUri);
    }

    public void setMessageListener(Consumer<String> listener) {
        this.messageListener = listener;
    }

    @Override
    public void onOpen(ServerHandshake handshakedata) {
        log.info("连接已打开");
    }

    @Override
    public void onMessage(String message) {
        log.info("收到消息: " + message);
        if (messageListener != null) {
            messageListener.accept(message);
        }
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        log.info("连接关闭，原因: " + reason);
    }

    @Override
    public void onError(Exception ex) {
        log.info("连接出错: " + ex.getMessage());
    }
}
