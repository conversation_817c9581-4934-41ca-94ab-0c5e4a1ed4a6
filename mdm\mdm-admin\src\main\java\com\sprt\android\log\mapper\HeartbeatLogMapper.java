package com.sprt.android.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.android.log.domain.HeartbeatLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 心跳日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Repository
public interface HeartbeatLogMapper extends BaseMapper<HeartbeatLog> {
    /**
     * 查询心跳日志
     *
     * @param id 心跳日志主键
     * @return 心跳日志
     */
    public HeartbeatLog selectHeartbeatLogById(Long id);

    /**
     * 查询心跳日志列表
     *
     * @param heartbeatLog 心跳日志
     * @return 心跳日志集合
     */
    public List<HeartbeatLog> selectHeartbeatLogList(HeartbeatLog heartbeatLog);

    /**
     * 新增心跳日志
     *
     * @param heartbeatLog 心跳日志
     * @return 结果
     */
    public int insertHeartbeatLog(HeartbeatLog heartbeatLog);

    /**
     * 修改心跳日志
     *
     * @param heartbeatLog 心跳日志
     * @return 结果
     */
    public int updateHeartbeatLog(HeartbeatLog heartbeatLog);

    /**
     * 删除心跳日志
     *
     * @param id 心跳日志主键
     * @return 结果
     */
    public int deleteHeartbeatLogById(Long id);

    /**
     * 批量删除心跳日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHeartbeatLogByIds(Long[] ids);

    /**
     * 查询每台设备最新心跳日志
     * @return
     */
    List<HeartbeatLog> selectLastHeartbeatLog();

    /**
     * 根据设备编号查询设备最新心跳日志
     * @param sn
     * @return
     */
    HeartbeatLog selectLastHeartbeatLogBySn(@Param("sn") String sn);


    List<HeartbeatLog> selectLastHeartbeatLogsBySnList(List<String> collect);
}