package com.sprt.android.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.android.log.domain.PushLogApk;

import java.util.List;

/**
 * 推送日志设备上apkidService接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface PushLogApkService extends IService<PushLogApk> {
    /**
     * 查询推送日志设备上apkid
     *
     * @param id 推送日志设备上apkid主键
     * @return 推送日志设备上apkid
     */
    public PushLogApk selectPushLogApkById(Long id);

    /**
     * 查询推送日志设备上apkid列表
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 推送日志设备上apkid集合
     */
    public List<PushLogApk> selectPushLogApkList(PushLogApk pushLogApk);

    /**
     * 新增推送日志设备上apkid
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 结果
     */
    public int insertPushLogApk(PushLogApk pushLogApk);

    /**
     * 修改推送日志设备上apkid
     *
     * @param pushLogApk 推送日志设备上apkid
     * @return 结果
     */
    public int updatePushLogApk(PushLogApk pushLogApk);

    /**
     * 批量删除推送日志设备上apkid
     *
     * @param ids 需要删除的推送日志设备上apkid主键集合
     * @return 结果
     */
    public int deletePushLogApkByIds(Long[] ids);

    /**
     * 删除推送日志设备上apkid信息
     *
     * @param id 推送日志设备上apkid主键
     * @return 结果
     */
    public int deletePushLogApkById(Long id);
}