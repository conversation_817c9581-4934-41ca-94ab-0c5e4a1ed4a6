package com.sprt.pc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.pc.domain.Group;
import com.sprt.pc.dto.DeviceGroupDTO;
import com.sprt.pc.vo.GroupVO;

import java.util.List;

/**
 * 分组Service接口
 *
 * <AUTHOR>
 * @date 2025-04-01
 */
public interface GroupService extends IService<Group> {
    /**
     * 查询分组
     *
     * @param id 分组主键
     * @return 分组
     */
    public Group selectGroupById(Long id);

    /**
     * 查询分组列表
     *
     * @param group 分组
     * @return 分组集合
     */
    public List<GroupVO> selectGroupList(Group group);

    /**
     * 新增分组
     *
     * @param group 分组
     * @return 结果
     */
    public int insertGroup(Group group);

    /**
     * 修改分组
     *
     * @param group 分组
     * @return 结果
     */
    public int updateGroup(Group group);

    /**
     * 批量删除分组
     *
     * @param ids 需要删除的分组主键集合
     * @return 结果
     */
    public int deleteGroupByIds(Long[] ids);

    /**
     * 删除分组信息
     *
     * @param id 分组主键
     * @return 结果
     */
    public int deleteGroupById(Long id);

    /**
     * 更新设备组信息
     * @param deviceGroupDTO
     * @return
     */
    int updateDeviceGroup(DeviceGroupDTO deviceGroupDTO);
}