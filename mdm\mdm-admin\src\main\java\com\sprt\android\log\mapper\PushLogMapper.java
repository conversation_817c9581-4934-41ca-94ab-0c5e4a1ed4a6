package com.sprt.android.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprt.android.log.domain.PushLog;
import com.sprt.android.log.vo.PushLogVO;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 推送日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Repository
public interface PushLogMapper extends BaseMapper<PushLog> {
    /**
     * 查询推送日志
     *
     * @param id 推送日志主键
     * @return 推送日志
     */
    public PushLog selectPushLogById(Long id);

    /**
     * 查询推送日志列表
     *
     * @param pushLog 推送日志
     * @return 推送日志集合
     */
    public List<PushLogVO> selectPushLogList(PushLog pushLog);

    /**
     * 新增推送日志
     *
     * @param pushLog 推送日志
     * @return 结果
     */
    public int insertPushLog(PushLog pushLog);

    /**
     * 修改推送日志
     *
     * @param pushLog 推送日志
     * @return 结果
     */
    public int updatePushLog(PushLog pushLog);

    /**
     * 删除推送日志
     *
     * @param id 推送日志主键
     * @return 结果
     */
    public int deletePushLogById(Long id);

    /**
     * 批量删除推送日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePushLogByIds(Long[] ids);
}