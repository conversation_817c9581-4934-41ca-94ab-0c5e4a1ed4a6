<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprt.pc.mapper.ApkMapper">

    <resultMap type="Apk" id="ApkResult">
        <result property="id" column="id"/>
        <result property="apkImage" column="apk_image"/>
        <result property="apkName" column="apk_name"/>
        <result property="apkPackage" column="apk_package"/>
        <result property="filePath" column="file_path"/>
        <result property="num" column="num"/>
        <result property="version" column="version"/>
        <result property="versionCode" column="version_code"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="apkPath" column="apk_path"/>
    </resultMap>

    <sql id="selectApkVo">
        select id,
               apk_image,
               apk_name,
               apk_package,
               file_path,
               num,
               version,
               version_code,
               description,
               create_time,
               apk_path
        from tb_apk
    </sql>

    <select id="selectApkList" parameterType="Apk" resultMap="ApkResult">
        <include refid="selectApkVo"/>
        <where>
            <if test="apkImage != null  and apkImage != ''">and apk_image = #{apkImage}</if>
            <if test="apkName != null  and apkName != ''">and apk_name like concat('%', #{apkName}, '%')</if>
            <if test="apkPackage != null  and apkPackage != ''">and apk_package = #{apkPackage}</if>
            <if test="filePath != null  and filePath != ''">and file_path = #{filePath}</if>
            <if test="num != null ">and num = #{num}</if>
            <if test="version != null  and version != ''">and version = #{version}</if>
            <if test="versionCode != null  and versionCode != ''">and version_code = #{versionCode}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="apkPath != null  and apkPath != ''">and apk_path = #{apkPath}</if>
        </where>
    </select>

    <select id="selectApkById" parameterType="Long" resultMap="ApkResult">
        <include refid="selectApkVo"/>
        where id = #{id}
    </select>

    <select id="selectApkByFilePath" resultType="com.sprt.pc.domain.Apk" resultMap="ApkResult">
        select id,
               apk_image,
               apk_name,
               apk_package,
               file_path,
               num,
               version,
               version_code,
               description,
               create_time,
               apk_path
        from tb_apk
        where file_path = #{filePath}
    </select>
    <select id="selectApkListByPath" resultType="com.sprt.pc.domain.Apk" parameterType="com.sprt.android.dto.ApkDTO">
        select id,
        apk_image,
        apk_name,
        apk_package,
        file_path,
        num,
        version,
        version_code,
        description,
        create_time,
        apk_path
        from tb_apk
        <where>
            <if test="path != null and path!= ''">
                and file_path like concat(#{path},'%')
            </if>
            <if test="apkName != null and apkName!= ''">
                and apk_name like concat('%',#{apkName},'%')
            </if>
        </where>
    </select>
    <select id="selectApkByApkId" resultType="com.sprt.pc.domain.Apk">
        select id,
        apk_image,
        apk_name,
        apk_package,
        file_path,
        num,
        version,
        version_code,
        description,
        create_time,
        apk_path
        from tb_apk
        where id in
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertApk" parameterType="Apk">
        insert into tb_apk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="apkImage != null">apk_image,</if>
            <if test="apkName != null">apk_name,</if>
            <if test="apkPackage != null">apk_package,</if>
            <if test="filePath != null">file_path,</if>
            <if test="num != null">num,</if>
            <if test="version != null">version,</if>
            <if test="versionCode != null">version_code,</if>
            <if test="description != null">description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="apkPath != null">apk_path,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="apkImage != null">#{apkImage},</if>
            <if test="apkName != null">#{apkName},</if>
            <if test="apkPackage != null">#{apkPackage},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="num != null">#{num},</if>
            <if test="version != null">#{version},</if>
            <if test="versionCode != null">#{versionCode},</if>
            <if test="description != null">#{description},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="apkPath != null">#{apkPath},</if>
        </trim>
    </insert>

    <update id="updateApk" parameterType="Apk">
        update tb_apk
        <trim prefix="SET" suffixOverrides=",">
            <if test="apkImage != null">apk_image = #{apkImage},</if>
            <if test="apkName != null">apk_name = #{apkName},</if>
            <if test="apkPackage != null">apk_package = #{apkPackage},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="num != null">num = #{num},</if>
            <if test="version != null">version = #{version},</if>
            <if test="versionCode != null">version_code = #{versionCode},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="apkPath != null">apk_path = #{apkPath},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApkById" parameterType="Long">
        delete
        from tb_apk
        where id = #{id}
    </delete>

    <delete id="deleteApkByIds" parameterType="String">
        delete from tb_apk where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>