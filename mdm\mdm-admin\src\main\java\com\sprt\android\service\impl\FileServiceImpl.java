package com.sprt.android.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sprt.android.dto.FileInfo;
import com.sprt.android.dto.GroupUpdateDTO;
import com.sprt.android.dto.SendFileDTO;
import com.sprt.android.log.domain.OperationLog;
import com.sprt.android.log.mapper.OperationLogMapper;
import com.sprt.android.service.FileService;
import com.sprt.android.utils.MyWebSocketClient;
import com.sprt.common.utils.StringUtils;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.domain.Device;
import com.sprt.pc.domain.GroupApk;
import com.sprt.pc.mapper.ApkMapper;
import com.sprt.pc.mapper.DeviceMapper;
import com.sprt.pc.mapper.GroupApkMapper;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.handshake.HandshakeImpl1Server;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private ApkMapper apkMapper;

    @Autowired
    private GroupApkMapper groupApkMapper;

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    public String sendMessageToAndroid(SendFileDTO sendFileDTO){
        log.info("获取到的数据："+sendFileDTO.toString());
        List<Device> devices = deviceMapper.selectList(new LambdaQueryWrapper<Device>().in(Device::getId, sendFileDTO.getDeviceIds()));
        OperationLog operationLog = new OperationLog();
        operationLog.setIsSuccess("成功");
        if("download".equals(sendFileDTO.getValue())){
            operationLog.setType("安装指令");
            operationLog.setSubdivisionType("APK安装");
        }else if("unDownload".equals(sendFileDTO.getValue())){
            operationLog.setType("卸载指令");
            operationLog.setSubdivisionType("APK卸载");
        }
        for (Device device : devices) {
            threadPoolTaskExecutor.execute(() -> {
                MyWebSocketClient client = null;
                operationLog.setDeviceId(device.getId());
                try{
                    URI uri = new URI("ws://" + device.getIp() + ":5008");
                    client = new MyWebSocketClient(uri);
                    client.connectBlocking();
                    client.onOpen(new HandshakeImpl1Server());
                    ArrayList<FileInfo> fileInfoList = new ArrayList<>();
                    GroupUpdateDTO groupUpdateDTO = new GroupUpdateDTO();
                    groupUpdateDTO.setType(sendFileDTO.getType());
                    groupUpdateDTO.setValue(sendFileDTO.getValue());
                    groupUpdateDTO.setCheckedPackage(sendFileDTO.getCheckedPackage());
                    if(sendFileDTO.getIsAllGroup()){
                        // 查找设备所在的分组的策略
                        List<GroupApk> groupApks = groupApkMapper.selectList(new LambdaQueryWrapper<GroupApk>().eq(GroupApk::getGroupId, device.getGroupId()));
                        List<Long> apkIds = groupApks.stream().map(GroupApk::getApkId).collect(Collectors.toList());
                        List<Apk> apkList = apkMapper.selectList(new LambdaQueryWrapper<Apk>().in(Apk::getId, apkIds));

                        for (Apk apk : apkList) {
                            FileInfo fileInfo = new FileInfo();
                            String relativePath = null;
                            String[] parts = apk.getFilePath().split("filePath");
                            if (parts.length > 1) {
                                relativePath = parts[1].replace("\\", "/");
                            }
                            if (StringUtils.isEmpty(relativePath)) {
                                continue;
                            }
                            fileInfo.setFilePath(relativePath);
                            fileInfo.setPackageName(apk.getApkPackage());
                            fileInfo.setVersion(apk.getVersion());
                            fileInfo.setApkName(apk.getApkName());
                            fileInfo.setStoreLocation(apk.getApkPath());
                            fileInfoList.add(fileInfo);
                        }
                        groupUpdateDTO.setFileInfoList(fileInfoList);
                        ArrayList<GroupUpdateDTO> groupUpdateDTOS = new ArrayList<>();
                        groupUpdateDTOS.add(groupUpdateDTO);
                        log.info("发送数据：" + JSON.toJSONString(groupUpdateDTOS));
                        client.send(JSON.toJSONString(groupUpdateDTOS));
                        operationLog.setContent("发送指令成功");
                        client.setMessageListener(message -> {
                            log.info("来自设备 [" + device.getIp() + "] 的响应: " + message);
                        });
                    }else {

                        if(sendFileDTO.getApkIds().isEmpty()){
                            client.close();
                            return;
                        }
                        List<Apk> apkList = apkMapper.selectList(new LambdaQueryWrapper<Apk>().in(Apk::getId, sendFileDTO.getApkIds()));
                        for (Apk apk : apkList) {
                            FileInfo fileInfo = new FileInfo();
                            String relativePath = null;
                            String[] parts = apk.getFilePath().split("filePath");
                            if (parts.length > 1) {
                                relativePath = parts[1].replace("\\", "/");
                            }
                            if (StringUtils.isEmpty(relativePath)) {
                                continue;
                            }
                            fileInfo.setFilePath(relativePath);
                            fileInfo.setPackageName(apk.getApkPackage());
                            fileInfo.setVersion(apk.getVersion());
                            fileInfo.setStoreLocation(apk.getApkPath());
                            fileInfo.setApkName(apk.getApkName());
                            fileInfoList.add(fileInfo);
                        }
                        groupUpdateDTO.setFileInfoList(fileInfoList);
                        ArrayList<GroupUpdateDTO> groupUpdateDTOS = new ArrayList<>();
                        groupUpdateDTOS.add(groupUpdateDTO);
                        log.info("发送数据：" + JSON.toJSONString(groupUpdateDTOS));
                        client.send(JSON.toJSONString(groupUpdateDTOS));
                        operationLog.setContent("发送指令成功");
                        client.setMessageListener(message -> {
                            log.info("来自设备 [" + device.getIp() + "] 的响应: " + message);
                        });
                    }
                    client.close();
                }catch (Exception e){
                    log.error("连接异常", e);
                    operationLog.setContent("发送指令失败，错误原因：" + e);
                    if(client != null){
                        client.close();
                    }
                }finally {
                    operationLogMapper.insert(operationLog);
                }

            });
        }
        return "任务已发送";
    }
}
