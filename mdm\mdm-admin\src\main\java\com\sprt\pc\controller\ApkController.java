package com.sprt.pc.controller;

import com.sprt.common.annotation.Log;
import com.sprt.common.core.controller.BaseController;
import com.sprt.common.core.domain.AjaxResult;
import com.sprt.common.core.page.TableDataInfo;
import com.sprt.common.enums.BusinessType;
import com.sprt.common.utils.poi.ExcelUtil;
import com.sprt.pc.domain.Apk;
import com.sprt.pc.service.ApkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * apk信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@RestController
@RequestMapping("/pc/apk")
public class ApkController extends BaseController {
    @Autowired
    private ApkService apkService;

    /**
     * 查询apk信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(Apk apk) {
        startPage();
        List<Apk> list = apkService.selectApkList(apk);
        return getDataTable(list);
    }

    /**
     * 导出apk信息列表
     */
    @Log(title = "apk信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Apk apk) {
        List<Apk> list = apkService.selectApkList(apk);
        ExcelUtil<Apk> util = new ExcelUtil<Apk>(Apk.class);
        util.exportExcel(response, list, "apk信息数据");
    }

    /**
     * 获取apk信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(apkService.selectApkById(id));
    }

    /**
     * 新增apk信息
     */
    @Log(title = "apk信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Apk apk) {
        return toAjax(apkService.insertApk(apk));
    }

    /**
     * 修改apk信息
     */
    @Log(title = "apk信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Apk apk) {
        return toAjax(apkService.updateApk(apk));
    }

    /**
     * 删除apk信息
     */
    @Log(title = "apk信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int flag = apkService.deleteApkByIds(ids);

        if (flag == 0) {
            return error("删除失败");
        } else if (flag == 1) {
            return error("删除失败，组策略已分配该apk");
        }
        return success();
    }
}
