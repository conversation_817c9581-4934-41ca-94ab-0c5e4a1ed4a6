package com.sprt.android.log.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推送日志设备上apkid对象 tb_push_log_apk
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tb_push_log_apk")
public class PushLogApk {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * $column.columnComment
     */
    private Long pushLogId;

    /**
     * $column.columnComment
     */
    private Long deviceId;

    /**
     * $column.columnComment
     */
    private String apkIds;

}
