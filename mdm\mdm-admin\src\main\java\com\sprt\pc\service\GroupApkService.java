package com.sprt.pc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sprt.pc.domain.GroupApk;
import com.sprt.pc.dto.GroupApkDTO;
import com.sprt.pc.vo.GroupApkVO;

import java.util.List;

/**
 * 组与apk绑定Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface GroupApkService extends IService<GroupApk> {
    /**
     * 查询组与apk绑定
     *
     * @param id 组与apk绑定主键
     * @return 组与apk绑定
     */
    public List<GroupApkVO> selectGroupApkById(Long id);

    /**
     * 查询组与apk绑定列表
     *
     * @param groupApk 组与apk绑定
     * @return 组与apk绑定集合
     */
    public List<GroupApkVO> selectGroupApkList(GroupApk groupApk);

    /**
     * 新增组与apk绑定
     *
     * @param groupApkDTO 组与apk绑定
     * @return 结果
     */
    public int insertGroupApk(GroupApkDTO groupApkDTO);

    /**
     * 修改组与apk绑定
     *
     * @param groupApk 组与apk绑定
     * @return 结果
     */
    public int updateGroupApk(GroupApk groupApk);

    /**
     * 批量删除组与apk绑定
     *
     * @param ids 需要删除的组与apk绑定主键集合
     * @return 结果
     */
    public int deleteGroupApkByIds(Long[] ids);

    /**
     * 删除组与apk绑定信息
     *
     * @param id 组与apk绑定主键
     * @return 结果
     */
    public int deleteGroupApkById(Long id);

    /**
     * 获取apk图片
     * @param packageName
     * @param version
     * @return
     */
    String getApkImg(String packageName, String version);
}